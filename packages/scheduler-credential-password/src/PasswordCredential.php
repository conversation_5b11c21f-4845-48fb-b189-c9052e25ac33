<?php

namespace ITBM\SchedulerCredentialPassword;

use App\Scheduling\AbstractCredential;
use Filament\Forms\Components\TextInput;

class PasswordCredential extends AbstractCredential
{
    public static function getName(): string
    {
        return 'Password';
    }

    public static function getDescription(): string
    {
        return 'A single password for encryption or other uses.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'password' => '',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            TextInput::make(self::field('password'))
                ->label('Password')
                ->password()
                ->required()
                ->autocomplete(false)
                ->columnSpan(1),
        ];
    }
}

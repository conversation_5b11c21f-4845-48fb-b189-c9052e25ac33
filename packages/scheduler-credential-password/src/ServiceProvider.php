<?php

namespace ITBM\SchedulerCredentialPassword;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\CredentialsRegistry;
use ITBM\SchedulerCredentialPassword\PasswordCredential;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(CredentialsRegistry $registry): void
    {
        $registry->register(PasswordCredential::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

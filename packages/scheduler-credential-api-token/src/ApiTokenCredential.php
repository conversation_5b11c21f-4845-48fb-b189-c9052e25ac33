<?php

namespace ITBM\SchedulerCredentialApiToken;

use App\Scheduling\AbstractCredential;
use Filament\Forms\Components\TextInput;

class ApiTokenCredential extends AbstractCredential
{
    public static function getName(): string
    {
        return 'API Token';
    }

    public static function getDescription(): string
    {
        return 'An API token for authentication.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'token' => '',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            TextInput::make(self::field('token'))
                ->label('API Token')
                ->password()
                ->required(),
        ];
    }
}

<?php

namespace ITBM\SchedulerCredentialApiToken;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\CredentialsRegistry;
use ITBM\SchedulerCredentialApiToken\ApiTokenCredential;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(CredentialsRegistry $registry): void
    {
        $registry->register(ApiTokenCredential::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

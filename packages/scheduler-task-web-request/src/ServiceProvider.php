<?php

namespace ITBM\SchedulerTaskWebRequest;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\TaskRegistry;
use ITBM\SchedulerTaskWebRequest\WebRequestTask;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(TaskRegistry $registry): void
    {
        $registry->register(WebRequestTask::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

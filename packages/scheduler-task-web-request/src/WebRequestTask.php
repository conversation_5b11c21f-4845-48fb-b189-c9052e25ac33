<?php

namespace ITBM\SchedulerTaskWebRequest;

use App\Scheduling\AbstractTask;
use Filament\Forms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Http;
use Exception;
use ITBM\SchedulerCredentialUsernamePassword\UsernamePasswordCredential;

class WebRequestTask extends AbstractTask
{
    public static function getName(): string
    {
        return 'Web Request';
    }

    public static function getDescription(): string
    {
        return 'Make a web request to a URL';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'url' => '',
            'method' => 'GET',
            'basic_auth_credential_id' => null,
            'body' => '{}',
            'headers' => [],
            'timeout' => 30,
            'verify_ssl' => true,
            'follow_redirects' => true,
            'max_redirects' => 5,
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            Forms\Components\TextInput::make(self::field('url'))
                ->label('URL')
                ->required()
                ->url()
                ->helperText('The URL to make the request to'),
            Forms\Components\Select::make(self::field('method'))
                ->label('Method')
                ->options([
                    'GET' => 'GET',
                    'POST' => 'POST',
                    'PUT' => 'PUT',
                    'PATCH' => 'PATCH',
                    'DELETE' => 'DELETE',
                    'HEAD' => 'HEAD',
                    'OPTIONS' => 'OPTIONS',
                ])
                ->live()
                ->required(),
            self::credentialSelect('basic_auth_credential_id', UsernamePasswordCredential::class)
                ->label('Basic Authentication')
                ->helperText('Optional: Select a username/password credential for basic authentication.')
                ->nullable(),
            Forms\Components\Textarea::make(self::field('body'))
                ->label('Body')
                ->placeholder('{"key": "value", "example": 123}')
                ->helperText('The body of the request in JSON format')
                ->hidden(fn (Forms\Get $get): bool => 
                    in_array($get(self::field('method')), ['GET', 'DELETE', 'HEAD', 'OPTIONS'])),
            Forms\Components\KeyValue::make(self::field('headers'))
                ->label('Headers')
                ->keyLabel('Header Name')
                ->valueLabel('Header Value')
                ->helperText('The headers of the request'),
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\TextInput::make(self::field('timeout'))
                        ->label('Timeout (seconds)')
                        ->numeric()
                        ->minValue(1)
                        ->maxValue(300)
                        ->helperText('Maximum time to wait for a response'),
                    Forms\Components\Toggle::make(self::field('verify_ssl'))
                        ->label('Verify SSL')
                        ->helperText('Verify SSL certificates'),
                ]),
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Toggle::make(self::field('follow_redirects'))
                        ->label('Follow Redirects')
                        ->helperText('Follow redirects automatically'),
                    Forms\Components\TextInput::make(self::field('max_redirects'))
                        ->label('Max Redirects')
                        ->numeric()
                        ->minValue(0)
                        ->maxValue(10)
                        ->helperText('Maximum redirects to follow')
                        ->hidden(fn (Forms\Get $get): bool => 
                            !$get(self::field('follow_redirects'))),
                ]),
        ];
    }

    public function handle(): void
    {
        $method = strtolower($this->configuration['method']);
        $url = $this->configuration['url'];
        $headers = $this->configuration['headers'];
        $timeout = $this->configuration['timeout'];
        $verifySsl = $this->configuration['verify_ssl'];
        $followRedirects = $this->configuration['follow_redirects'];
        $maxRedirects = $this->configuration['max_redirects'];
        $credentialId = $this->configuration['basic_auth_credential_id'];
        
        echo "Making {$this->configuration['method']} request to {$url}\n\n";
        echo "Headers: " . json_encode($headers, JSON_PRETTY_PRINT) . "\n\n";

        $credential = static::loadCredentials($credentialId);

        if ($credential) {
            $username = $credential['username'];
            $password = $credential['password'];

            $headers['Authorization'] = 'Basic ' . base64_encode($username . ':' . $password);
            echo "Using Basic Authentication with username: {$username}\n\n";
        }
        
        // Initialize the HTTP client with configuration
        $client = Http::withHeaders($headers)
            ->timeout($timeout)
            ->withOptions([
                'verify' => $verifySsl,
                'allow_redirects' => $followRedirects ? [
                    'max' => $maxRedirects,
                    'strict' => true,
                    'referer' => true,
                    'protocols' => ['http', 'https'],
                    'track_redirects' => true,
                ] : false,
            ]);
        
        $response = null;
        
        // For methods that don't typically have a body
        if (in_array(strtoupper($method), ['GET', 'DELETE', 'HEAD', 'OPTIONS'])) {
            $response = $client->$method($url);
        } else {
            // For methods that may have a body
            $body = $this->configuration['body'];
            
            echo "Request body: " . (is_string($body) ? $body : json_encode($body, JSON_PRETTY_PRINT)) . "\n";
            
            // If body is a JSON string, try to decode it
            if (is_string($body) && !empty($body)) {
                try {
                    $decodedBody = json_decode($body, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $body = $decodedBody;
                    }
                } catch (Exception $e) {
                    // Leave body as is if JSON decoding fails
                }
            }

            $response = $client->$method($url, $body);
        }
        
        // Output the response details
        echo "\nResponse received in " . $response->handlerStats()['total_time'] . " seconds\n";
        echo "Status: " . $response->status() . "\n";
        echo "Headers: " . json_encode($response->headers(), JSON_PRETTY_PRINT) . "\n";
        
        // For non-binary responses, output the body
        $contentType = $response->header('Content-Type');
        if ($contentType && !preg_match('/(image|audio|video|application\/octet-stream)/i', $contentType)) {
            echo "Body: " . $response->body() . "\n";
        } else {
            echo "Body: [Binary content]\n";
        }
        
        // Throw an exception for non-2xx responses
        if (!$response->successful()) {
            throw new \Exception("Request failed with status code " . $response->status());
        }
    }
}

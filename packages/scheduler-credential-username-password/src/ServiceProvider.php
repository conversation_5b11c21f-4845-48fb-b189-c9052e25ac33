<?php

namespace ITBM\SchedulerCredentialUsernamePassword;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\CredentialsRegistry;
use ITBM\SchedulerCredentialUsernamePassword\UsernamePasswordCredential;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(CredentialsRegistry $registry): void
    {
        $registry->register(UsernamePasswordCredential::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

<?php

namespace ITBM\SchedulerCredentialUsernamePassword;

use App\Scheduling\AbstractCredential;
use Filament\Forms\Components\TextInput;

class UsernamePasswordCredential extends AbstractCredential
{
    public static function getName(): string
    {
        return 'Username / Password';
    }

    public static function getDescription(): string
    {
        return 'A username and password combination.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'username' => '',
            'password' => '',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            TextInput::make(self::field('username'))
                ->label('Username')
                ->required(),
            TextInput::make(self::field('password'))
                ->label('Password')
                ->password()
                ->required(),
        ];
    }
}

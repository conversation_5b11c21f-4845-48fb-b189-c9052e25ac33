<?php

namespace ITBM\SchedulerCredentialAws;

use App\Scheduling\AbstractCredential;
use Filament\Forms\Components\TextInput;

class AWSCredential extends AbstractCredential
{
    public static function getName(): string
    {
        return 'AWS Credentials';
    }

    public static function getDescription(): string
    {
        return 'AWS access key ID and secret access key for accessing AWS services.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'access_key_id' => '',
            'secret_access_key' => '',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            TextInput::make(self::field('access_key_id'))
                ->label('Access Key ID')
                ->required()
                ->autocomplete(false)
                ->columnSpan(1),
            TextInput::make(self::field('secret_access_key'))
                ->label('Secret Access Key')
                ->password()
                ->required()
                ->autocomplete(false)
                ->columnSpan(1),
        ];
    }
}

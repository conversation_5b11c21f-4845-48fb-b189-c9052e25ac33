<?php

namespace ITBM\SchedulerCredentialAws;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\CredentialsRegistry;
use ITBM\SchedulerCredentialAws\AWSCredential;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(CredentialsRegistry $registry): void
    {
        $registry->register(AWSCredential::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

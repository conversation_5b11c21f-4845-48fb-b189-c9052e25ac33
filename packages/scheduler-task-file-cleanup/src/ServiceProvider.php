<?php

namespace ITBM\SchedulerTaskFileCleanup;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\TaskRegistry;
use ITBM\SchedulerTaskFileCleanup\FileCleanupTask;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(TaskRegistry $registry): void
    {
        $registry->register(FileCleanupTask::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

<?php

namespace ITBM\SchedulerTaskFileCleanup;

use App\Scheduling\AbstractTask;
use App\Scheduling\Integrations\S3BucketIntegration;
use Aws\S3\S3Client;
use Filament\Forms;
use Illuminate\Support\Facades\Storage;

class FileCleanupTask extends AbstractTask
{
    public static function getName(): string
    {
        return 'File Cleanup';
    }

    public static function getDescription(): string
    {
        return 'Delete old files from local storage or an S3 bucket';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'storage_type' => 'local',
            'local_path' => storage_path('backups/postgresql'),
            's3_integration_id' => null,
            's3_path_prefix' => '',
            'older_than_value' => 7,
            'older_than_unit' => 'days',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            Forms\Components\Select::make(self::field('storage_type'))
                ->label('Storage Type')
                ->options([
                    'local' => 'Local Storage',
                    's3' => 'S3 Bucket',
                ])
                ->default('local')
                ->reactive()
                ->required()
                ->columnSpan(2),
            Forms\Components\TextInput::make(self::field('local_path'))
                ->label('Local Path')
                ->required()
                ->visible(fn (Forms\Get $get) => $get(self::field('storage_type')) === 'local')
                ->columnSpan(2),
            self::integrationSelect('s3_integration_id', S3BucketIntegration::class)
                ->label('S3 Bucket')
                ->required()
                ->visible(fn (Forms\Get $get) => $get(self::field('storage_type')) === 's3')
                ->columnSpan(2),
            Forms\Components\TextInput::make(self::field('s3_path_prefix'))
                ->label('S3 Path Prefix (Optional)')
                ->placeholder('e.g., backups/postgres')
                ->visible(fn (Forms\Get $get) => $get(self::field('storage_type')) === 's3')
                ->columnSpan(2),
            Forms\Components\TextInput::make(self::field('older_than_value'))
                ->label('Delete files older than')
                ->numeric()
                ->default(7)
                ->required()
                ->columnSpan(1),
            Forms\Components\Select::make(self::field('older_than_unit'))
                ->options([
                    'minutes' => 'Minutes',
                    'hours' => 'Hours',
                    'days' => 'Days',
                    'weeks' => 'Weeks',
                    'months' => 'Months',
                    'years' => 'Years',
                ])
                ->default('days')
                ->required()
                ->columnSpan(1),
        ];
    }

    public function handle(): void
    {
        $storageType = $this->configuration['storage_type'];
        $olderThanValue = $this->configuration['older_than_value'];
        $olderThanUnit = $this->configuration['older_than_unit'];

        $cutoffTimestamp = now()->sub($olderThanValue, $olderThanUnit)->timestamp;

        if ($storageType === 'local') {
            $this->handleLocalStorage($cutoffTimestamp);
        } else {
            $this->handleS3Storage($cutoffTimestamp);
        }
    }

    private function handleLocalStorage(int $cutoffTimestamp): void
    {
        $localPath = $this->configuration['local_path'];

        if (!is_dir($localPath)) {
            echo "Local path {$localPath} does not exist.\n";
            return;
        }

        $files = glob($localPath . '/*');

        if (empty($files)) {
            echo "No files found in {$localPath}.\n";
            return;
        }

        $deletedFiles = 0;
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTimestamp) {
                echo "Deleting file {$file}\n";
                unlink($file);
                $deletedFiles++;
            }
        }

        if ($deletedFiles === 0) {
            echo "No files found older than the specified date in {$localPath}.\n";
        }
    }

    private function handleS3Storage(int $cutoffTimestamp): void
    {
        $s3IntegrationId = $this->configuration['s3_integration_id'];
        $s3Integration = static::loadIntegration($s3IntegrationId);

        if (!$s3Integration) {
            throw new \Exception("S3 integration not found with ID: {$s3IntegrationId}");
        }

        $s3PathPrefix = $this->configuration['s3_path_prefix'] ?? '';

        $s3Client = new S3Client([
            'version' => 'latest',
            'region'  => $s3Integration['region'],
            'credentials' => [
                'key'    => $s3Integration['credentials']['access_key_id'],
                'secret' => $s3Integration['credentials']['secret_access_key'],
            ],
            'endpoint' => $s3Integration['endpoint'] ?: null,
            'use_path_style_endpoint' => $s3Integration['use_path_style_endpoint'],
        ]);

        $objects = $s3Client->listObjectsV2([
            'Bucket' => $s3Integration['bucket'],
            'Prefix' => $s3PathPrefix,
        ]);

        $deletedFiles = 0;
        if ($objects['Contents']) {
            foreach ($objects['Contents'] as $object) {
                $lastModifiedTimestamp = strtotime($object['LastModified']);
                if ($lastModifiedTimestamp < $cutoffTimestamp) {
                    echo "Deleting s3://" . $s3Integration['bucket'] . "/" . $object['Key'] . "\n";
                    $s3Client->deleteObject([
                        'Bucket' => $s3Integration['bucket'],
                        'Key' => $object['Key'],
                    ]);
                    $deletedFiles++;
                }
            }
        } else {
            echo "No files found in s3://" . $s3Integration['bucket'] . "/" . $s3PathPrefix . "\n";
        }

        if ($deletedFiles === 0) {
            echo "No files found older than the specified date in s3://" . $s3Integration['bucket'] . "/" . $s3PathPrefix . "\n";
        }
    }
}

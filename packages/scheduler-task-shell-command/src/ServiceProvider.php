<?php

namespace ITBM\SchedulerTaskShellCommand;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\TaskRegistry;
use ITBM\SchedulerTaskShellCommand\ShellCommandTask;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(TaskRegistry $registry): void
    {
        $registry->register(ShellCommandTask::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

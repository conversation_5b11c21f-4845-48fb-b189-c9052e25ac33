<?php

namespace ITBM\SchedulerTaskShellCommand;

use App\Scheduling\AbstractTask;
use Filament\Forms;
use Illuminate\Support\Facades\Process;

class ShellCommandTask extends AbstractTask
{
    public static function getName(): string
    {
        return 'Shell Command';
    }

    public static function getDescription(): string
    {
        return 'Execute a shell command on the server';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'command' => '',
            'working_directory' => '',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            Forms\Components\TextInput::make(self::field('command'))
                ->label('Shell Command')
                ->required()
                ->helperText('The shell command to execute'),
            
            Forms\Components\TextInput::make(self::field('working_directory'))
                ->label('Working Directory')
                ->helperText('Directory where the command will be executed'),
        ];
    }

    public function handle(): void
    {
        $process = Process::command($this->configuration['command']);
        if (!empty($this->configuration['working_directory'])) {
            $process->path($this->configuration['working_directory']);
        }
        
        // Run the process and capture output
        $result = $process->run();
        
        // Output both stdout and stderr
        echo "STDOUT:\n" . $result->output() . "\n\n";
        echo "STDERR:\n" . $result->errorOutput() . "\n\n";
        echo "Exit Code: " . $result->exitCode() . "\n";
        
        // Throw an exception if process failed
        if (!$result->successful()) {
            throw new \Exception("Command failed with exit code " . $result->exitCode());
        }
    }
}

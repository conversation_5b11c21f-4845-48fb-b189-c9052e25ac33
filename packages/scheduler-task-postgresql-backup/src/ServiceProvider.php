<?php

namespace ITBM\SchedulerTaskPostgresqlBackup;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\TaskRegistry;
use ITBM\SchedulerTaskPostgresqlBackup\PostgreSQLBackupTask;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(TaskRegistry $registry): void
    {
        $registry->register(PostgreSQLBackupTask::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

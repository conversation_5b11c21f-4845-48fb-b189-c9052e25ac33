<?php

namespace ITBM\SchedulerTaskPostgresqlBackup;

use App\Scheduling\AbstractTask;
use App\Scheduling\Integrations\PostgreSQLServerIntegration;
use App\Scheduling\Integrations\S3BucketIntegration;
use Aws\S3\S3Client;
use Filament\Forms;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;

class PostgreSQLBackupTask extends AbstractTask
{
    public static function getName(): string
    {
        return 'PostgreSQL Backup';
    }

    public static function getDescription(): string
    {
        return 'Create a backup of a PostgreSQL database';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'server' => null,
            'storage' => [
                'type' => 'local',
                'local_path' => storage_path('backups/postgresql'),
                's3_integration_id' => null,
                's3_path_prefix' => '',
            ],
            'encryption_credential_id' => null,
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            self::integrationSelect('server', PostgreSQLServerIntegration::class)
                ->label('PostgreSQL Server')
                ->required()
                ->columnSpan(2),
            Forms\Components\Select::make(self::field('storage.type'))
                ->label('Storage Type')
                ->options([
                    'local' => 'Local Storage',
                    's3' => 'S3 Bucket',
                ])
                ->default('local')
                ->reactive()
                ->required()
                ->columnSpan(2),
            Forms\Components\TextInput::make(self::field('storage.local_path'))
                ->label('Backup Path')
                ->required()
                ->visible(fn (Forms\Get $get) => $get(self::field('storage.type')) === 'local')
                ->columnSpan(2),
            self::integrationSelect('storage.s3_integration_id', S3BucketIntegration::class)
                ->label('S3 Bucket')
                ->required()
                ->visible(fn (Forms\Get $get) => $get(self::field('storage.type')) === 's3')
                ->columnSpan(2),
            Forms\Components\TextInput::make(self::field('storage.s3_path_prefix'))
                ->label('S3 Path Prefix (Optional)')
                ->placeholder('e.g., backups/postgres')
                ->visible(fn (Forms\Get $get) => $get(self::field('storage.type')) === 's3')
                ->columnSpan(2),
            self::credentialSelect('encryption_credential_id', \App\Scheduling\Credentials\PasswordCredential::class)
                ->label('Encryption Password (Optional)')
                ->columnSpan(2),
        ];
    }

    public function handle(): void
    {
        $integrationId = $this->configuration['server'];
        $storageType = $this->configuration['storage']['type'];
        $encryptionPasswordId = $this->configuration['encryption_credential_id'];

        $integration = static::loadIntegration($integrationId);

        if (!$integration) {
            throw new \Exception("PostgreSQL integration not found with ID: {$integrationId}");
        }

        $host = $integration['host'];
        $port = $integration['port'];
        $database = $integration['database'] ?? 'postgres';
        $username = $integration['credentials']['username'];
        $password = $integration['credentials']['password'];
        $sslmode = $integration['sslmode'] ?? 'prefer';
        
        echo "Starting PostgreSQL backup\n\n";
        echo "Database: {$database} on {$host}:{$port}\n\n";
        echo "Storage Type: " . ucfirst($storageType) . "\n\n";
        
        // Create temporary directory for backup
        $tempDir = sys_get_temp_dir() . '/postgres_backup_' . uniqid();
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        // Generate backup filename with timestamp
        $filename = $database . '_' . date('Y-m-d_H-i-s') . '.sql';
        $tempPath = $tempDir . '/' . $filename;
        $encryptedPath = $tempPath . '.enc';
        
        echo "Creating temporary backup at: {$tempPath}\n\n";
        
        try {
            // Set up pg_dump command
            $command = "PGPASSWORD=\"{$password}\" pg_dump";
            $command .= " --host={$host}";
            $command .= " --port={$port}";
            $command .= " --username={$username}";
            $command .= " --format=plain";
            $command .= " --sslmode={$sslmode}";
            $command .= " --file={$tempPath}";
            $command .= " {$database}";
            
            // Execute the backup command
            $process = Process::run($command);
            
            if (!$process->successful()) {
                throw new \Exception("PostgreSQL backup failed: " . $process->errorOutput());
            }
            
            $size = filesize($tempPath);
            $sizeInMb = round($size / 1024 / 1024, 2);
            echo "Backup created successfully: {$sizeInMb} MB\n\n";
            
            // Handle encryption
            if ($encryptionPasswordId) {
                $encryptionCredential = static::loadCredentials($encryptionPasswordId);
                if (!$encryptionCredential || !isset($encryptionCredential['password'])) {
                    throw new \Exception("Encryption password not found or invalid.");
                }
                $encryptionPassword = $encryptionCredential['password'];

                echo "Encrypting backup...\n\n";
                $encryptionCommand = "openssl enc -aes-256-cbc -salt -pbkdf2 -in {$tempPath} -out {$encryptedPath} -k \"{$encryptionPassword}\"";
                $encryptionProcess = Process::run($encryptionCommand);

                if (!$encryptionProcess->successful()) {
                    throw new \Exception("Encryption failed: " . $encryptionProcess->errorOutput());
                }

                echo "Backup encrypted successfully.\n\n";

                // Delete the original unencrypted file
                unlink($tempPath);
                $tempPath = $encryptedPath; // Update $tempPath to point to the encrypted file
                $filename = $filename . '.enc'; // Update $filename to reflect the encrypted file
            }
            
            // Handle storage based on type
            if ($storageType === 'local') {
                $this->handleLocalStorage($tempPath, $filename);
            } else {
                $this->handleS3Storage($tempPath, $filename);
            }
            
            echo "Backup stored successfully\n\n";
            
        } finally {
            // Cleanup temporary files
            if (file_exists($tempPath) && $tempPath === $encryptedPath) {
                unlink($tempPath);
            }
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
            if (is_dir($tempDir)) {
                rmdir($tempDir);
            }
        }
    }
    
    private function handleLocalStorage(string $tempPath, string $filename): void
    {
        $localPath = $this->configuration['storage']['local_path'];
        
        if (!is_dir($localPath)) {
            echo "Creating backup directory: {$localPath}\n\n";
            mkdir($localPath, 0755, true);
        }
        
        $destinationPath = $localPath . '/' . $filename;
        echo "Moving backup to: {$destinationPath}\n\n";
        
        rename($tempPath, $destinationPath);
    }
    
    private function handleS3Storage(string $tempPath, string $filename): void
    {
        $s3IntegrationId = $this->configuration['storage']['s3_integration_id'];
        $s3Integration = static::loadIntegration($s3IntegrationId);

        if (!$s3Integration) {
            throw new \Exception("S3 integration not found with ID: {$s3IntegrationId}");
        }

        $s3PathPrefix = $this->configuration['storage']['s3_path_prefix'] ?? '';

        // Construct the S3 key
        $s3Key = $filename;
        if (!empty($s3PathPrefix)) {
            // Ensure prefix ends with a slash if it's not empty
            $prefix = rtrim($s3PathPrefix, '/') . '/';
            $s3Key = $prefix . $filename;
        }

        echo "Uploading to S3 bucket: {$s3Integration['bucket']} with key: {$s3Key}\n\n";

        $s3Client = new S3Client([
            'version' => 'latest',
            'region'  => $s3Integration['region'],
            'credentials' => [
                'key'    => $s3Integration['credentials']['access_key_id'],
                'secret' => $s3Integration['credentials']['secret_access_key'],
            ],
            'endpoint' => $s3Integration['endpoint'] ?: null,
            'use_path_style_endpoint' => $s3Integration['use_path_style_endpoint'],
        ]);

        // Upload with exponential backoff retry
        $maxAttempts = 3;
        $attempt = 0;
        $lastError = null;

        while ($attempt < $maxAttempts) {
            try {
                $s3Client->putObject([
                    'Bucket' => $s3Integration['bucket'],
                    'Key'    => $s3Key,
                    'Body'   => fopen($tempPath, 'r'),
                ]);
                echo "Upload successful\n\n";
                return;
            } catch (\Exception $e) {
                $lastError = $e;
                $attempt++;
                if ($attempt < $maxAttempts) {
                    $delay = pow(2, $attempt);
                    echo "Upload attempt {$attempt} failed, retrying in {$delay} seconds...\n\n";
                    sleep($delay);
                }
            }
        }

        throw new \Exception("Failed to upload to S3 after {$maxAttempts} attempts: " . $lastError->getMessage());
    }

}

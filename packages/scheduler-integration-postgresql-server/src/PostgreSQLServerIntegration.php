<?php

namespace ITBM\SchedulerIntegrationPostgresqlServer;

use App\Scheduling\AbstractIntegration;
use ITBM\SchedulerCredentialUsernamePassword\UsernamePasswordCredential;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

class PostgreSQLServerIntegration extends AbstractIntegration
{
    /**
     * Returns the health status of the PostgreSQL integration.
     * @param array|null $config
     * @return bool|null
     */
    // public static function getHealthStatus(?array $config = null): ?bool
    // {
    //     if (!$config) {
    //         return null;
    //     }

    //     try {
    //         $dsn = "pgsql:host={$config['host']};port={$config['port']}";

    //         if (!empty($config['database'])) {
    //             $dsn .= ";dbname={$config['database']}";
    //         }

    //         $pdo = new \PDO(
    //             $dsn,
    //             $config['credentials']['username'] ?? '',
    //             $config['credentials']['password'] ?? '',
    //             [
    //                 \PDO::ATTR_TIMEOUT => 5,
    //                 \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
    //             ]
    //         );

    //         $pdo->query('SELECT 1');
    //         return true;
    //     } catch (\Throwable $e) {
    //         return false;
    //     }
    // }

    public static function getName(): string
    {
        return 'PostgreSQL Server';
    }

    public static function getDescription(): string
    {
        return 'Connect to a PostgreSQL database server.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'host' => 'localhost',
            'port' => 5432,
            'credential_id' => null,
            'database' => '',
            'sslmode' => 'prefer',
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            TextInput::make(self::field('host'))
                ->label('Host')
                ->required()
                ->columnSpan(1),
            TextInput::make(self::field('port'))
                ->label('Port')
                ->numeric()
                ->required()
                ->columnSpan(1),
            self::credentialSelect('credential_id', UsernamePasswordCredential::class)
                ->label('Credentials')
                ->helperText('Select the credentials for connecting to the PostgreSQL server.')
                ->required(),
            TextInput::make(self::field('database'))
                ->label('Database')
                ->helperText('Optional: Default database to connect to.')
                ->columnSpan(1),
            TextInput::make(self::field('sslmode'))
                ->label('SSL Mode')
                ->helperText('e.g., disable, allow, prefer, require, verify-ca, verify-full.')
                ->columnSpan(1),
        ];
    }
}

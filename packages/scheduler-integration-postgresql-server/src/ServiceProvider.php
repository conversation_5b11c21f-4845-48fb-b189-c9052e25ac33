<?php

namespace ITBM\SchedulerIntegrationPostgresqlServer;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\IntegrationsRegistry;
use ITBM\SchedulerIntegrationPostgresqlServer\PostgreSQLServerIntegration;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(IntegrationsRegistry $registry): void
    {
        $registry->register(PostgreSQLServerIntegration::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

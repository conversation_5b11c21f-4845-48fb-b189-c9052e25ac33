<?php

namespace ITBM\SchedulerIntegrationS3Bucket;

use App\Scheduling\AbstractIntegration;
use ITBM\SchedulerCredentialAws\AWSCredential;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;

class S3BucketIntegration extends AbstractIntegration
{
    /**
     * Returns the health status of the S3 integration.
     * @param array|null $config
     * @return bool|null
     */
    // public static function getHealthStatus(?array $config = null): ?bool
    // {
    //     if (!$config) {
    //         return null;
    //     }

    //     try {
    //         $s3Client = new \Aws\S3\S3Client([
    //             'version' => 'latest',
    //             'region'  => $config['region'] ?? '',
    //             'credentials' => [
    //                 'key'    => $config['credentials']['access_key_id'] ?? '',
    //                 'secret' => $config['credentials']['secret_access_key'] ?? '',
    //             ],
    //             'endpoint' => !empty($config['endpoint']) ? $config['endpoint'] : null,
    //             'use_path_style_endpoint' => $config['use_path_style_endpoint'] ?? false,
    //         ]);

    //         if (!empty($config['bucket'])) {
    //             $s3Client->headBucket(['Bucket' => $config['bucket']]);
    //         } else {
    //             $s3Client->listBuckets();
    //         }
    //         return true;
    //     } catch (\Throwable $e) {
    //         return false;
    //     }
    // }

    public static function getName(): string
    {
        return 'S3 Bucket';
    }

    public static function getDescription(): string
    {
        return 'Connect to an S3 compatible object storage bucket.';
    }

    public static function getDefaultConfiguration(): array
    {
        return [
            'credential_id' => null,
            'bucket' => '',
            'region' => '',
            'endpoint' => '',
            'use_path_style_endpoint' => false,
        ];
    }

    public static function getConfigurationSchema(): array
    {
        return [
            self::credentialSelect('credential_id', AWSCredential::class)
                ->label('AWS Credentials')
                ->helperText('Select the AWS credentials for this S3 bucket.')
                ->required()
                ->columnSpan(2),
            TextInput::make(self::field('bucket'))
                ->label('Bucket Name')
                ->required()
                ->columnSpan(1),
            TextInput::make(self::field('region'))
                ->label('Region')
                ->helperText('e.g., us-east-1')
                ->required()
                ->columnSpan(1),
            TextInput::make(self::field('endpoint'))
                ->label('Endpoint URL')
                ->helperText('Optional: For S3-compatible services (e.g., MinIO, DigitalOcean Spaces). Leave empty for AWS S3.')
                ->url()
                ->columnSpan(2),
            Select::make(self::field('use_path_style_endpoint'))
                ->label('Use Path Style Endpoint')
                ->options([
                    true => 'Yes',
                    false => 'No',
                ])
                ->default(false)
                ->helperText('Required for some S3-compatible services like MinIO.')
                ->columnSpan(2),
        ];
    }
}

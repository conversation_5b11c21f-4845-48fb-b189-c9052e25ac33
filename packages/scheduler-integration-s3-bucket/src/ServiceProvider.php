<?php

namespace ITBM\SchedulerIntegrationS3Bucket;

use Illuminate\Support\ServiceProvider as BaseServiceProvider;
use App\Scheduling\IntegrationsRegistry;
use ITBM\SchedulerIntegrationS3Bucket\S3BucketIntegration;

class ServiceProvider extends BaseServiceProvider
{
    public function boot(IntegrationsRegistry $registry): void
    {
        $registry->register(S3BucketIntegration::class);
    }

    public function register(): void
    {
        // Register bindings if needed
    }
}

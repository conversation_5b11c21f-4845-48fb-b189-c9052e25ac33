# Scheduler

A flexible task scheduling system built with <PERSON><PERSON> and <PERSON>lam<PERSON>, designed to create, manage, and execute recurring tasks of various types.

## Description

The Scheduler is a web application designed to create, manage, and execute recurring tasks of various types. It provides a flexible system for scheduling automated operations with different frequencies, from seconds to yearly intervals.

### Core Components

#### 1. Task System
- **Task Model**: Represents scheduled tasks with configuration, frequency, and status
- **Taskable Contract**: Interface that task implementations must follow
- **Task Types**:
  - `WebRequestTask`: Makes HTTP requests to specified URLs
  - `ShellCommandTask`: Executes shell commands on the server
  - `PostgreSQLBackupTask`: Creates database backups

#### 2. Scheduling Engine
- **TaskRegistry**: Manages and provides access to all available task types
- **AbstractTask**: Base class with common functionality for all tasks
- **TaskScheduler**: Core service that registers tasks with <PERSON><PERSON>'s scheduler
- **ExecuteScheduledTask**: Job that handles actual task execution

#### 3. User Interface
- **Admin Panel**: For system administration (via Filament)
- **User Panel**: Interface for regular users to manage their tasks
- **Task Resource**: UI components for task management

### Architecture

The application follows <PERSON><PERSON>'s MVC architecture with additional layers:

1. **Models**: Define data structure and relationships (Task, User)
2. **Contracts**: Define interfaces for implementation (Taskable)
3. **Services**: Contain business logic (TaskScheduler)
4. **Jobs**: Handle background processing (ExecuteScheduledTask)
5. **Providers**: Handle service registration and bootstrapping (TaskServiceProvider)

### Data Flow

1. Users create tasks through the web interface
2. Tasks are stored in the database with their configuration and schedule
3. On application startup, TaskScheduler loads all active tasks from the database
4. For each task, a scheduled job is created based on its frequency settings
5. When a task is due to run, Laravel's scheduler dispatches the ExecuteScheduledTask job
6. The job resolves the appropriate task implementation through the TaskRegistry
7. The task is executed and its status is updated in the database
8. Results can be viewed in the admin/user interface

### Technologies

- **Backend**: Laravel PHP framework
- **Frontend**: Filament admin panel (TALL stack: Tailwind, Alpine.js, Laravel, Livewire)
- **Database**: Supports PostgreSQL (with backup capabilities) and other Laravel-supported databases
- **Development**: Docker environment via Laravel Sail
- **Queuing**: Laravel's queue system for background processing

## License

...

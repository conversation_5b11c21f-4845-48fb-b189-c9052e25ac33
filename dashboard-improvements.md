itbm: Look in to the #codebase , it has a Filamentphp integration called `App`. I'd like the dashboard to be improved with relevant information and widgets. 

Have a think about what might be useful to include on the dashboard and come up with a comprehensive plan to implement it. 

GitHub Copilot: # Dashboard Improvement Plan for Scheduler App

After examining your codebase, I can see you have a Laravel application with Filament PHP that manages scheduled tasks, integrations, and credentials. The current dashboard is likely using Filament's default widgets, but we can significantly improve it with task-specific information and actionable insights.

## Recommended Dashboard Widgets

### 1. Task Status Overview Card

A summary card showing:
- Total tasks count
- Active vs. inactive tasks
- Tasks grouped by type (File Cleanup, Web Request, Shell Command, etc.)
- Tasks with errors or warnings

### 2. Recent Task Executions

A timeline or list of the most recent task executions with:
- Task name
- Execution status (success/failed/running)
- Runtime duration
- Quick link to view full execution details

### 3. Task Execution Stats Chart

A visual representation of:
- Success/failure rates over time
- Execution durations
- Task types distribution

### 4. Upcoming Tasks Calendar

Show tasks scheduled to run in the next 24 hours or next week with:
- Task name
- Scheduled execution time
- Frequency type

### 5. Failed Tasks Alert List

Highlight tasks that have failed recently with:
- Task name
- Last failure time
- Error message preview
- Quick action to retry

### 6. Integration Health Status

Monitor the health of your integrations:
- S3 connections
- PostgreSQL connections
- Other services

### 7. Quick Action Buttons

Contextual buttons for common actions:
- Create new task
- Run task now
- View all executions
- Manage integrations

## Implementation Plan

### Step 1: Create Custom Widgets

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\Task;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TaskOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '10s';

    protected function getStats(): array
    {
        $totalTasks = Task::count();
        $activeTasks = Task::where('is_active', true)->count();
        $inactiveTasks = $totalTasks - $activeTasks;
        $failedTasks = Task::where('last_run_status', 'failed')->count();
        
        return [
            Stat::make('Total Tasks', $totalTasks)
                ->description('All configured tasks')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('primary'),
                
            Stat::make('Active Tasks', $activeTasks)
                ->description('Currently scheduled')
                ->descriptionIcon('heroicon-m-bolt')
                ->color('success'),
                
            Stat::make('Inactive Tasks', $inactiveTasks)
                ->description('Not currently scheduled')
                ->descriptionIcon('heroicon-m-bolt-slash')
                ->color('gray'),
                
            Stat::make('Failed Tasks', $failedTasks)
                ->description('Last execution failed')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color('danger'),
        ];
    }
}
```

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\TaskExecution;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class RecentTaskExecutionsWidget extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $pollingInterval = '15s';
    
    protected int | string | array $columnSpan = 'full';
    
    public function table(Table $table): Table
    {
        return $table
            ->query(
                TaskExecution::query()
                    ->with('task')
                    ->latest('started_at')
                    ->limit(5)
            )
            ->columns([
                TextColumn::make('task.name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'success' => 'success',
                        'running' => 'info',
                        'failed' => 'danger',
                        default => 'warning',
                    }),
                TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('runtime_seconds')
                    ->label('Runtime')
                    ->formatStateUsing(fn ($state) => $state.' seconds'),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('view')
                    ->url(fn (TaskExecution $record) => route('filament.app.resources.tasks.edit', ['record' => $record->task_id]) . '?activeRelationManager=1')
                    ->icon('heroicon-m-eye'),
            ]);
    }
}
```

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\TaskExecution;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class TaskExecutionChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Task Execution History';
    protected static ?string $pollingInterval = '30s';
    
    protected static ?int $sort = 3;
    
    protected function getData(): array
    {
        $days = 7;
        $period = now()->subDays($days)->format('Y-m-d');
        
        $successfulExecutions = $this->getExecutionsCountByStatus('success', $period);
        $failedExecutions = $this->getExecutionsCountByStatus('failed', $period);
        
        $labels = collect(range(0, $days - 1))
            ->map(fn ($daysAgo) => now()->subDays($daysAgo)->format('M d'))
            ->reverse()
            ->toArray();
        
        return [
            'datasets' => [
                [
                    'label' => 'Successful',
                    'data' => $successfulExecutions,
                    'fill' => false,
                    'borderColor' => 'rgb(75, 192, 192)',
                    'tension' => 0.1,
                ],
                [
                    'label' => 'Failed',
                    'data' => $failedExecutions,
                    'fill' => false,
                    'borderColor' => 'rgb(255, 99, 132)',
                    'tension' => 0.1,
                ],
            ],
            'labels' => $labels,
        ];
    }
    
    private function getExecutionsCountByStatus(string $status, string $fromDate): array
    {
        return TaskExecution::where('status', $status)
            ->where('started_at', '>=', $fromDate)
            ->select(DB::raw('DATE(started_at) as date'), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }
    
    protected function getType(): string
    {
        return 'line';
    }
}
```

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\Task;
use App\Services\TaskScheduler;
use Carbon\Carbon;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class UpcomingTasksWidget extends BaseWidget
{
    protected static ?string $heading = 'Upcoming Task Executions';
    protected static ?string $pollingInterval = '60s';
    
    protected static ?int $sort = 4;
    
    public function table(Table $table): Table
    {
        $scheduler = app(TaskScheduler::class);
        
        // Get all active tasks
        $tasks = Task::where('is_active', true)->get();
        
        // Calculate next run times
        $tasksWithNextRun = $tasks->map(function ($task) use ($scheduler) {
            $nextRun = $scheduler->calculateNextRunTime($task);
            $task->next_run = $nextRun;
            return $task;
        })
        // Filter tasks scheduled in the next 24 hours
        ->filter(function ($task) {
            return $task->next_run && 
                   $task->next_run->isFuture() && 
                   $task->next_run->diffInHours(now()) <= 24;
        })
        // Sort by next run time
        ->sortBy('next_run');
        
        return $table
            ->query(
                Task::whereIn('id', $tasksWithNextRun->pluck('id'))
            )
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                IconColumn::make('is_active')
                    ->boolean(),
                TextColumn::make('next_run')
                    ->label('Next Run')
                    ->formatStateUsing(fn ($record) => $tasksWithNextRun->firstWhere('id', $record->id)->next_run->diffForHumans()),
                TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('run')
                    ->icon('heroicon-m-play')
                    ->color('success')
                    ->action(function (Task $record) {
                        \App\Jobs\ExecuteScheduledTask::dispatch($record);
                        
                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Task queued')
                            ->body('The task has been queued for execution')
                            ->send();
                    }),
            ]);
    }
}
```

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\Task;
use App\Models\TaskExecution;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class FailedTasksWidget extends BaseWidget
{
    protected static ?string $heading = 'Recently Failed Tasks';
    protected static ?string $pollingInterval = '20s';
    
    protected static ?int $sort = 5;
    
    public function table(Table $table): Table
    {
        // Get tasks that have recently failed
        $failedTaskIds = TaskExecution::where('status', 'failed')
            ->where('completed_at', '>=', now()->subDays(3))
            ->pluck('task_id')
            ->unique();
        
        return $table
            ->query(
                Task::whereIn('id', $failedTaskIds)
                    ->where('is_active', true)
            )
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_run_at')
                    ->dateTime(),
                TextColumn::make('lastExecution.output')
                    ->label('Error Message')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        return $column->getState();
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('retry')
                    ->icon('heroicon-m-arrow-path')
                    ->color('warning')
                    ->action(function (Task $record) {
                        \App\Jobs\ExecuteScheduledTask::dispatch($record);
                        
                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Task queued')
                            ->body('The task has been queued for execution')
                            ->send();
                    }),
            ]);
    }
}
```

```php
<?php

namespace App\Filament\App\Widgets;

use App\Models\Integration;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Services\IntegrationHealthChecker;

class IntegrationsStatusWidget extends BaseWidget
{
    protected static ?string $heading = 'Integration Health';
    protected static ?string $pollingInterval = '5m';
    
    protected static ?int $sort = 6;
    
    public function table(Table $table): Table
    {
        $healthChecker = app(IntegrationHealthChecker::class);
        
        return $table
            ->query(
                Integration::query()
            )
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    }),
                IconColumn::make('health_status')
                    ->label('Status')
                    ->getStateUsing(function (Integration $record) use ($healthChecker) {
                        return $healthChecker->check($record) ? 'healthy' : 'unhealthy';
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'healthy' => 'heroicon-o-check-circle',
                        'unhealthy' => 'heroicon-o-exclamation-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'healthy' => 'success',
                        'unhealthy' => 'danger',
                        default => 'warning',
                    }),
                TextColumn::make('last_checked_at')
                    ->getStateUsing(function (Integration $record) use ($healthChecker) {
                        $lastCheck = $healthChecker->lastChecked($record);
                        return $lastCheck ? $lastCheck->diffForHumans() : 'Never';
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('check')
                    ->icon('heroicon-m-arrow-path')
                    ->action(function (Integration $record, IntegrationHealthChecker $healthChecker) {
                        $isHealthy = $healthChecker->check($record, true);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Integration Health Check')
                            ->body($isHealthy ? 'Integration is healthy' : 'Integration check failed')
                            ->color($isHealthy ? 'success' : 'danger')
                            ->send();
                    }),
            ]);
    }
}
```

### Step 2: Create Supporting Services

```php
<?php

namespace App\Services;

use App\Models\Integration;
use App\Scheduling\Integrations\S3BucketIntegration;
use App\Scheduling\Integrations\PostgreSQLServerIntegration;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class IntegrationHealthChecker
{
    /**
     * Check the health of an integration
     *
     * @param Integration $integration
     * @param bool $forceCheck Force a new check instead of using cached results
     * @return bool
     */
    public function check(Integration $integration, bool $forceCheck = false): bool
    {
        $cacheKey = "integration_health_{$integration->id}";
        
        if (!$forceCheck && Cache::has($cacheKey)) {
            return Cache::get($cacheKey)['status'];
        }
        
        $isHealthy = false;
        
        try {
            // Load the integration configuration with resolved credentials
            $config = $this->resolveIntegrationConfig($integration);
            
            if (!$config) {
                throw new \Exception("Could not resolve integration configuration");
            }
            
            // Check based on integration type
            if ($integration->type === S3BucketIntegration::class) {
                $isHealthy = $this->checkS3Integration($config);
            } elseif ($integration->type === PostgreSQLServerIntegration::class) {
                $isHealthy = $this->checkPostgresIntegration($config);
            } else {
                // Default to true for unknown integration types
                $isHealthy = true;
            }
        } catch (\Throwable $e) {
            $isHealthy = false;
        }
        
        // Cache the result
        Cache::put($cacheKey, [
            'status' => $isHealthy,
            'checked_at' => now(),
        ], now()->addHours(1));
        
        return $isHealthy;
    }
    
    /**
     * Get the last time the integration was checked
     *
     * @param Integration $integration
     * @return \Carbon\Carbon|null
     */
    public function lastChecked(Integration $integration): ?Carbon
    {
        $cacheKey = "integration_health_{$integration->id}";
        
        if (Cache::has($cacheKey)) {
            return Carbon::parse(Cache::get($cacheKey)['checked_at']);
        }
        
        return null;
    }
    
    /**
     * Resolve the integration configuration with credentials
     * 
     * @param Integration $integration
     * @return array|null
     */
    private function resolveIntegrationConfig(Integration $integration): ?array
    {
        $integrationClass = $integration->type;
        
        if (!class_exists($integrationClass) || !method_exists($integrationClass, 'loadIntegration')) {
            return null;
        }
        
        return $integrationClass::loadIntegration($integration->id);
    }
    
    /**
     * Check S3 integration health
     * 
     * @param array $config
     * @return bool
     */
    private function checkS3Integration(array $config): bool
    {
        $s3Client = new \Aws\S3\S3Client([
            'version' => 'latest',
            'region'  => $config['region'],
            'credentials' => [
                'key'    => $config['credentials']['access_key_id'] ?? '',
                'secret' => $config['credentials']['secret_access_key'] ?? '',
            ],
            'endpoint' => !empty($config['endpoint']) ? $config['endpoint'] : null,
            'use_path_style_endpoint' => $config['use_path_style_endpoint'] ?? false,
        ]);
        
        // Just list buckets or check if the specified bucket exists
        try {
            if (!empty($config['bucket'])) {
                $s3Client->headBucket(['Bucket' => $config['bucket']]);
            } else {
                $s3Client->listBuckets();
            }
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
    
    /**
     * Check PostgreSQL integration health
     * 
     * @param array $config
     * @return bool
     */
    private function checkPostgresIntegration(array $config): bool
    {
        try {
            $dsn = "pgsql:host={$config['host']};port={$config['port']}";
            
            if (!empty($config['database'])) {
                $dsn .= ";dbname={$config['database']}";
            }
            
            $pdo = new \PDO(
                $dsn,
                $config['credentials']['username'] ?? '',
                $config['credentials']['password'] ?? '',
                [
                    \PDO::ATTR_TIMEOUT => 5,
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                ]
            );
            
            // Simple query to test the connection
            $pdo->query('SELECT 1');
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
}
```

```php
<?php

namespace App\Services;

use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;

class TaskScheduler
{
    /**
     * Calculate the next time a task will run based on its frequency
     *
     * @param Task $task
     * @return Carbon|null
     */
    public function calculateNextRunTime(Task $task): ?Carbon
    {
        if (!$task->is_active) {
            return null;
        }

        // Get the timezone from task parameters or use UTC as default
        $timezone = $task->frequency_parameters['timezone'] ?? 'UTC';
        $now = Carbon::now($timezone);
        
        // Handle cron expressions
        if ($task->frequency_type === 'cron' && isset($task->frequency_parameters['cron'])) {
            return $this->getNextRunFromCronExpression(
                $task->frequency_parameters['cron'],
                $now
            );
        }

        // Handle time-based frequencies
        if ($task->frequency_type === 'time_based' && $task->frequency) {
            return $this->getNextRunFromTimeBasedFrequency(
                $task->frequency,
                $task->frequency_parameters ?? [],
                $now
            );
        }

        return null;
    }

    /**
     * Calculate next run time from a cron expression
     *
     * @param string $expression
     * @param Carbon $now
     * @return Carbon
     */
    protected function getNextRunFromCronExpression(string $expression, Carbon $now): Carbon
    {
        $cron = new \Cron\CronExpression($expression);
        $nextRun = Carbon::instance($cron->getNextRunDate(
            $now->toDateTime(),
            0,
            false
        ));
        
        return $nextRun;
    }

    /**
     * Calculate next run time from a time-based frequency
     *
     * @param string $frequency
     * @param array $parameters
     * @param Carbon $now
     * @return Carbon|null
     */
    protected function getNextRunFromTimeBasedFrequency(string $frequency, array $parameters, Carbon $now): ?Carbon
    {
        $next = $now->copy();
        
        switch ($frequency) {
            case 'everySecond':
                return $next->addSecond();
                
            case 'everyTwoSeconds':
                return $next->addSeconds(2 - ($next->second % 2));
                
            case 'everyFiveSeconds':
                return $next->addSeconds(5 - ($next->second % 5));
                
            case 'everyTenSeconds':
                return $next->addSeconds(10 - ($next->second % 10));
                
            case 'everyFifteenSeconds':
                return $next->addSeconds(15 - ($next->second % 15));
                
            case 'everyThirtySeconds':
                return $next->addSeconds(30 - ($next->second % 30));
                
            case 'everyMinute':
                return $next->addMinute()->startOfMinute();
                
            case 'everyTwoMinutes':
                return $next->addMinutes(2 - ($next->minute % 2))->startOfMinute();
                
            case 'everyThreeMinutes':
                return $next->addMinutes(3 - ($next->minute % 3))->startOfMinute();
                
            case 'everyFiveMinutes':
                return $next->addMinutes(5 - ($next->minute % 5))->startOfMinute();
                
            case 'everyTenMinutes':
                return $next->addMinutes(10 - ($next->minute % 10))->startOfMinute();
                
            case 'everyFifteenMinutes':
                return $next->addMinutes(15 - ($next->minute % 15))->startOfMinute();
                
            case 'everyThirtyMinutes':
                return $next->addMinutes(30 - ($next->minute % 30))->startOfMinute();
                
            case 'hourly':
                return $next->addHour()->startOfHour();
                
            case 'hourlyAt':
                $minute = $parameters['minute'] ?? 0;
                if ($next->minute >= $minute) {
                    $next->addHour();
                }
                return $next->minute($minute)->second(0);
                
            case 'daily':
                return $next->addDay()->startOfDay();
                
            case 'dailyAt':
                $time = $parameters['time'] ?? '00:00';
                list($hour, $minute) = explode(':', $time);
                if ($next->hour > $hour || ($next->hour == $hour && $next->minute >= $minute)) {
                    $next->addDay();
                }
                return $next->hour($hour)->minute($minute)->second(0);
                
            case 'weekly':
                return $next->next(Carbon::SUNDAY)->startOfDay(); // Default to Sunday, can be parameterized

            case 'weeklyOn':
                $dayOfWeek = $parameters['day'] ?? Carbon::SUNDAY; // 0 for Sunday, 1 for Monday, etc.
                $time = $parameters['time'] ?? '00:00';
                list($hour, $minute) = explode(':', $time);
                $next->next($dayOfWeek);
                if ($now->dayOfWeek == $dayOfWeek && ($now->hour > $hour || ($now->hour == $hour && $now->minute >= $minute))) {
                    $next->addWeek();
                }
                return $next->hour($hour)->minute($minute)->second(0);

            case 'monthly':
                return $next->addMonth()->startOfMonth()->startOfDay();

            case 'monthlyOn':
                $dayOfMonth = $parameters['day'] ?? 1;
                $time = $parameters['time'] ?? '00:00';
                list($hour, $minute) = explode(':', $time);
                if ($now->day > $dayOfMonth || ($now->day == $dayOfMonth && ($now->hour > $hour || ($now->hour == $hour && $now->minute >= $minute)))) {
                    $next->addMonth();
                }
                return $next->day($dayOfMonth)->hour($hour)->minute($minute)->second(0);
            
            case 'quarterly':
                return $next->addMonths(3)->firstOfQuarter()->startOfDay();

            case 'yearly':
                return $next->addYear()->startOfYear()->startOfDay();

            case 'twiceDaily':
                $firstHour = $parameters['first_hour'] ?? 1;
                $secondHour = $parameters['second_hour'] ?? 13;
                if ($next->hour >= $secondHour) {
                    return $next->addDay()->hour($firstHour)->minute(0)->second(0);
                } elseif ($next->hour >= $firstHour) {
                    return $next->hour($secondHour)->minute(0)->second(0);
                }
                return $next->hour($firstHour)->minute(0)->second(0);

            case 'twiceDailyAt':
                $firstHour = $parameters['first_hour'] ?? 1;
                $firstMinute = $parameters['first_minute'] ?? 0;
                $secondHour = $parameters['second_hour'] ?? 13;
                $secondMinute = $parameters['second_minute'] ?? 0;

                $time1 = $now->copy()->hour($firstHour)->minute($firstMinute)->second(0);
                $time2 = $now->copy()->hour($secondHour)->minute($secondMinute)->second(0);

                if ($now->gte($time2)) {
                    return $time1->addDay();
                } elseif ($now->gte($time1)) {
                    return $time2;
                }
                return $time1;

            case 'weekdays':
                if ($next->isWeekend()) {
                    return $next->nextWeekday()->startOfDay();
                }
                return $next->addDay()->startOfDay(); // If today is a weekday, check next day

            case 'weekends':
                if ($next->isWeekday()) {
                    return $next->nextWeekendDay()->startOfDay();
                }
                return $next->addDay()->startOfDay(); // If today is a weekend day, check next day
            
            case 'mondays':
                return $next->next(Carbon::MONDAY)->startOfDay();
            case 'tuesdays':
                return $next->next(Carbon::TUESDAY)->startOfDay();
            case 'wednesdays':
                return $next->next(Carbon::WEDNESDAY)->startOfDay();
            case 'thursdays':
                return $next->next(Carbon::THURSDAY)->startOfDay();
            case 'fridays':
                return $next->next(Carbon::FRIDAY)->startOfDay();
            case 'saturdays':
                return $next->next(Carbon::SATURDAY)->startOfDay();
            case 'sundays':
                return $next->next(Carbon::SUNDAY)->startOfDay();

            // Add more specific cases as needed based on Laravel's scheduler options
            // e.g. everyFourMinutes, everySixHours etc.
            // For simplicity, the above covers common patterns.
            // A more robust solution might directly leverage Laravel's underlying schedule parsing if possible,
            // or require a more complex mapping.

            default:
                // Fallback for unknown or very complex frequencies
                // Consider logging a warning if an unhandled frequency is encountered
                return null; // Or throw an exception
        }
    }

    // ... other methods from the existing TaskScheduler
}
```

### Step 3: Register Widgets and Configure Dashboard

```php
<?php

namespace App\Providers\Filament;

use App\Filament\App\Widgets\TaskOverviewWidget;
use App\Filament\App\Widgets\RecentTaskExecutionsWidget;
use App\Filament\App\Widgets\TaskExecutionChartWidget;
use App\Filament\App\Widgets\UpcomingTasksWidget;
use App\Filament\App\Widgets\FailedTasksWidget;
use App\Filament\App\Widgets\IntegrationsStatusWidget;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('app')
            ->path('')
            ->login()
            ->passwordReset()
            ->profile()
            ->spa()
            ->unsavedChangesAlerts()
            ->databaseTransactions()
            ->colors([
                'primary' => Color::Red,
            ])
            ->discoverResources(in: app_path('Filament/App/Resources'), for: 'App\\Filament\\App\\Resources')
            ->discoverPages(in: app_path('Filament/App/Pages'), for: 'App\\Filament\\App\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/App/Widgets'), for: 'App\\Filament\\App\\Widgets')
            ->widgets([
                TaskOverviewWidget::class,
                RecentTaskExecutionsWidget::class,
                TaskExecutionChartWidget::class,
                UpcomingTasksWidget::class,
                FailedTasksWidget::class,
                IntegrationsStatusWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
```

### Step 4: Create a Custom Dashboard Page (Optional)

```php
/scheduler/src/app/Filament/App/Pages/Dashboard.php
<?php

namespace App\Filament\App\Pages;

use App\Filament\App\Widgets\TaskOverviewWidget;
use App\Filament\App\Widgets\RecentTaskExecutionsWidget;
use App\Filament\App\Widgets\TaskExecutionChartWidget;
use App\Filament\App\Widgets\UpcomingTasksWidget;
use App\Filament\App\Widgets\FailedTasksWidget;
use App\Filament\App\Widgets\IntegrationsStatusWidget;
use Filament\Actions\Action;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    protected static string $view = 'filament.app.pages.dashboard';
    
    protected function getHeaderActions(): array
    {
        return [
            Action::make('new_task')
                ->label('New Task')
                ->icon('heroicon-m-plus')
                ->url(route('filament.app.resources.tasks.create')),
                
            Action::make('view_executions')
                ->label('All Executions')
                ->icon('heroicon-m-clock')
                ->url('#'), // Update with the correct route
        ];
    }
    
    protected function getHeaderWidgets(): array
    {
        return [
            TaskOverviewWidget::class,
        ];
    }

    protected function getWidgets(): array
    {
        return [
            TaskExecutionChartWidget::class,
            RecentTaskExecutionsWidget::class,
            UpcomingTasksWidget::class,
            FailedTasksWidget::class,
            IntegrationsStatusWidget::class,
        ];
    }
}
```

## Final Steps

1. Create the necessary relationships in your models (if not already present).
2. Create a blade view for the custom dashboard page (e.g., `resources/views/filament/app/pages/dashboard.blade.php`).
3. Ensure all required frequency types are fully implemented in the `TaskScheduler::calculateNextRunTime` method.
4. Implement the `IntegrationHealthChecker` service.
5. **Dependencies**: Verify that `composer.json` includes `aws/aws-sdk-php` (if S3 integration is used) and `dragonmantank/cron-expression` (for cron parsing in `TaskScheduler`). Run `composer install` if new dependencies are added.
6. **Widget Polling**: Adjust the `protected static ?string $pollingInterval` in each widget class as needed for optimal data freshness and performance. Values like '5s', '10s', '30s', '1m', '5m', or `null` (to disable) can be used.

After implementing these changes, your dashboard will provide a comprehensive view of your scheduled tasks, recent executions, and system health, making it much more useful for daily operations.

<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    // Register scheduled tasks from the database
    ->withSchedule(function (\Illuminate\Console\Scheduling\Schedule $schedule) {
        try {
            \Illuminate\Support\Facades\DB::connection()->getPdo();

            // Only schedule if the tasks table exists
            if (\Illuminate\Support\Facades\Schema::hasTable('tasks')) {
                $tasks = \App\Models\Task::where('is_active', true)->get();
                $scheduler = app(\App\Services\TaskScheduler::class);
                foreach ($tasks as $task) {
                    $job = new \App\Jobs\ExecuteScheduledTask($task);
                    $event = $schedule->job($job)
                        ->withoutOverlapping()
                        ->onOneServer()
                        ->name("task:{$task->id}");
                    // Apply the schedule frequency
                    if ($task->frequency_type === 'cron') {
                        $event->cron($task->frequency_parameters['cron']);
                    } else {
                        $scheduler->applySchedule($event, $task->frequency, $task->frequency_parameters ?? []);
                    }
                    // Apply timezone if set
                    if (!empty($task->frequency_parameters['timezone'])) {
                        $event->timezone($task->frequency_parameters['timezone']);
                    }
                }
            }
        } catch (\PDOException $e) {
            \Illuminate\Support\Facades\Log::error("Database connection failed: " . $e->getMessage());
        }
    })
    ->create();

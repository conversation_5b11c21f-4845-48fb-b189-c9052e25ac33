<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_executions', function (Blueprint $table) {
            // Basic fields
            $table->ulid('id')->primary();
            $table->ulid('task_id');
            $table->foreign('task_id')->references('id')->on('tasks')->onDelete('cascade');
            
            // Execution information
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->string('status'); // running, success, failed
            $table->longText('output')->nullable();
            $table->longText('error')->nullable();
            $table->float('runtime_seconds', 8, 2)->nullable();
            
            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index('task_id');
            $table->index('status');
            $table->index('started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_executions');
    }
};
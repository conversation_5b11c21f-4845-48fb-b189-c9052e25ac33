<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            // Basic fields
            $table->ulid('id')->primary();
            $table->string('name');
            $table->longText('description')->nullable();
            $table->boolean('is_active');
            
            // Schedule fields
            $table->string('frequency_type');  // enum: time_based, cron
            $table->string('frequency')->nullable(); // stores the method name (everyMinute, hourly, etc.)
            $table->json('frequency_parameters')->nullable(); // stores any parameters for the frequency
            
            // Task type and configuration
            $table->string('type'); // shell, digitalocean_snapshot, etc.
            $table->text('configuration'); // stores type-specific configuration

            // Last run information
            $table->timestamp('last_run_at')->nullable();
            $table->string('last_run_status')->nullable();

            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};

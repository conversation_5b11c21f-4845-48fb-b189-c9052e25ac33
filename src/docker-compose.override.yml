services:
    laravel.test: # Overriding to add dependency on artisan-runner 
        depends_on:
            artisan-runner:
                condition: service_completed_successfully

    artisan-runner:
        build:
            context: './vendor/laravel/sail/runtimes/8.4'
            dockerfile: Dockerfile
            args: 
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.4/app'
        restart: 'no' # Run once and then stop
        extra_hosts: 
            - 'host.docker.internal:host-gateway'
        environment: 
            WWWUSER: '${WWWUSER}' 
            LARAVEL_SAIL: 1 
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}' 
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}' 
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes: 
            - '.:/var/www/html'
        networks: 
            - sail
        depends_on:
            pgsql:
                condition: service_healthy
        command: ["sh", "-c", "php artisan migrate && php artisan operations:process"]

    queue:
        build:
            context: './vendor/laravel/sail/runtimes/8.4'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.4/app'
        restart: unless-stopped
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        command: ["php", "artisan", "queue:listen", "--tries=3", "--backoff=3"]
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - laravel.test

    scheduler:
        build:
            context: './vendor/laravel/sail/runtimes/8.4'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.4/app'
        restart: unless-stopped
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        command: ["/bin/bash", "-c", "while true; do php artisan schedule:run --verbose --no-interaction & sleep 60; done"]
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - laravel.test

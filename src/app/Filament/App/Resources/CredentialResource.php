<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\CredentialResource\Pages;
use App\Models\Credential;
use App\Scheduling\CredentialsRegistry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Arr;

class CredentialResource extends Resource
{
    protected static ?string $model = Credential::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';

    public static function form(Form $form): Form
    {
        $registry = app(CredentialsRegistry::class);

        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description'),
                    ]),

                Forms\Components\Section::make('Credential Configuration')
                    ->description('Specific settings for the selected credential type.')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Credential Type')
                            ->options($registry->getCredentialOptions())
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $type = $state;
                                $set('configuration', $type::getDefaultConfiguration());
                            }),
                            
                        Forms\Components\Grid::make()
                            ->schema(function (Forms\Get $get) use ($registry) {
                                $type = $get('type');
                                if (!$type) {
                                    return [];
                                }
                                if (class_exists($type) && method_exists($type, 'getConfigurationSchema')) {
                                    return collect($type::getConfigurationSchema())->toArray();
                                }
                                return [];
                            })
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->wrap()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCredentials::route('/'),
            'create' => Pages\CreateCredential::route('/create'),
            'edit' => Pages\EditCredential::route('/{record}/edit'),
        ];
    }
}

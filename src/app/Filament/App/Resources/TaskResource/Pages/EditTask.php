<?php

namespace App\Filament\App\Resources\TaskResource\Pages;

use App\Filament\App\Resources\TaskResource;
use App\Jobs\ExecuteScheduledTask;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTask extends EditRecord
{
    protected static string $resource = TaskResource::class;

    public function getHeaderActions(): array
    {
        return [
            Actions\Action::make('run')
                ->label('Run Task')
                ->icon('heroicon-m-play')
                ->color('success')
                ->action(function () {
                    ExecuteScheduledTask::dispatch($this->record);
                    
                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Task queued')
                        ->body('The task has been queued for execution')
                        ->send();
                }),
            Actions\DeleteAction::make(),
        ];
    }
}

<?php

namespace App\Filament\App\Resources\TaskResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Colors\Color;

class ExecutionsRelationManager extends RelationManager
{
    protected static string $relationship = 'executions';

    protected static ?string $recordTitleAttribute = 'id';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'success' => 'success',
                        'running' => 'info',
                        'failed' => 'danger',
                        default => 'warning',
                    }),
                Tables\Columns\TextColumn::make('runtime_seconds')
                    ->label('Runtime')
                    ->formatStateUsing(function ($state) {
                        if ($state === null) return '-';
                        if ($state < 1) return '<1s';
                        return round($state) . 's';
                    })
                    ->sortable(),
            ])
            ->defaultSort('started_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'running' => 'Running',
                        'success' => 'Success',
                        'failed' => 'Failed',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Execution Details')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('id')
                                    ->label('ID'),
                                Infolists\Components\TextEntry::make('status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'success' => 'success',
                                        'running' => 'info',
                                        'failed' => 'danger',
                                        default => 'warning',
                                    }),
                                Infolists\Components\TextEntry::make('started_at')
                                    ->dateTime(),
                                Infolists\Components\TextEntry::make('completed_at')
                                    ->dateTime(),
                                Infolists\Components\TextEntry::make('runtime_seconds')
                                    ->label('Runtime')
                                    ->formatStateUsing(function ($state) {
                                        return $state.' seconds';
                                    }),
                            ]),
                    ]),
                
                Infolists\Components\Section::make('Output')
                    ->schema([
                        Infolists\Components\TextEntry::make('output')
                            ->label(false)
                            ->formatStateUsing(fn ($state) => $state ?: 'No output')
                            ->markdown()
                            ->extraAttributes(['class' => 'font-mono text-xs p-4 bg-gray-50 dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700 overflow-auto']),
                    ])
                    ->collapsible()
                    ->collapsed(false),
                
                Infolists\Components\Section::make('Error')
                    ->schema([
                        Infolists\Components\TextEntry::make('error')
                            ->label(false)
                            ->formatStateUsing(fn ($state) => $state ?: 'No errors')
                            ->markdown()
                            ->extraAttributes(['class' => 'font-mono text-xs p-4 bg-gray-50 dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700 overflow-auto text-danger-500']),
                    ])
                    ->collapsible()
                    ->collapsed(false)
                    ->hidden(fn ($record) => empty($record->error)),
            ]);
    }
}
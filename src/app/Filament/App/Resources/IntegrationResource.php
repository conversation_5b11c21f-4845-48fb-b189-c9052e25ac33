<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\IntegrationResource\Pages;
use App\Models\Integration;
use App\Scheduling\IntegrationsRegistry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Arr;

class IntegrationResource extends Resource
{
    protected static ?string $model = Integration::class;

    protected static ?string $navigationIcon = 'heroicon-o-server-stack';

    public static function form(Form $form): Form
    {
        $registry = app(IntegrationsRegistry::class);

        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description'),
                    ]),

                Forms\Components\Section::make('Integration Configuration')
                    ->description('Specific settings for the selected integration type.')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Integration Type')
                            ->options($registry->getIntegrationOptions())
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $type = $state;
                                $set('configuration', $type::getDefaultConfiguration());
                            }),

                        Forms\Components\Grid::make()
                            ->schema(function (Forms\Get $get) use ($registry) {
                                $type = $get('type');
                                if (!$type) {
                                    return [];
                                }
                                if (class_exists($type) && method_exists($type, 'getConfigurationSchema')) {
                                    return collect($type::getConfigurationSchema())->toArray();
                                }
                                return [];
                            })
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    })
                    ->searchable()
                    ->sortable(),
                \Filament\Tables\Columns\IconColumn::make('health_status')
                    ->label('Status')
                    ->getStateUsing(function ($record) {
                        $checker = app(\App\Services\IntegrationHealthChecker::class);
                        $status = $checker->check($record);
                        if ($status === true) {
                            return 'healthy';
                        } elseif ($status === false) {
                            return 'unhealthy';
                        } elseif ($status === null) {
                            return 'unknown';
                        }
                        return 'unknown';
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'healthy' => 'heroicon-o-check-circle',
                        'unhealthy' => 'heroicon-o-exclamation-circle',
                        'unknown' => 'heroicon-o-minus-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'healthy' => 'success',
                        'unhealthy' => 'danger',
                        'unknown' => 'secondary',
                        default => 'secondary',
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->wrap()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIntegrations::route('/'),
            'create' => Pages\CreateIntegration::route('/create'),
            'edit' => Pages\EditIntegration::route('/{record}/edit'),
        ];
    }
}

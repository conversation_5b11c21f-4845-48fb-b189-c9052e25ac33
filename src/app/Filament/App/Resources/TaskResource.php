<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TaskResource\Pages;
use App\Filament\App\Resources\TaskResource\RelationManagers;
use App\Models\Task;
use App\Scheduling\TaskRegistry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    public static function form(Form $form): Form
    {
        $registry = app(TaskRegistry::class);
        
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description'),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->onIcon('heroicon-m-bolt')
                            ->offIcon('heroicon-m-bolt-slash'),
                    ]),

                Forms\Components\Section::make('Schedule Configuration')
                    ->schema([
                        Forms\Components\Select::make('frequency_type')
                            ->options([
                                'time_based' => 'Time-based Schedule',
                                'cron' => 'Cron Expression',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $set('frequency_parameters', []);
                                if ($state === 'cron') {
                                    $set('frequency', null);
                                }
                            }),

                        // Time-based frequency options
                        Forms\Components\Select::make('frequency')
                            ->options(self::getFrequencyOptions())
                            ->visible(fn (Forms\Get $get) => $get('frequency_type') === 'time_based')
                            ->required(fn (Forms\Get $get) => $get('frequency_type') === 'time_based')
                            ->live()
                            ->afterStateUpdated(fn ($state, Forms\Set $set) => $set('frequency_parameters', [])),

                        // Frequency parameters grid (same as your original)
                        Forms\Components\Grid::make()
                            ->schema(self::getFrequencyParameterFields())
                            ->columns(2),

                        // Cron expression
                        Forms\Components\TextInput::make('frequency_parameters.cron')
                            ->visible(fn (Forms\Get $get) => $get('frequency_type') === 'cron')
                            ->required(fn (Forms\Get $get) => $get('frequency_type') === 'cron')
                            ->label('Cron Expression')
                            ->placeholder('* * * * *')
                            ->helperText('Enter a valid cron expression')
                            ->regex('/^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|(\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9]))) (\*|([0-9]|1[0-9]|2[0-3])|(\*\/([0-9]|1[0-9]|2[0-3]))) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|(\*\/([1-9]|1[0-9]|2[0-9]|3[0-1]))) (\*|([1-9]|1[0-2])|(\*\/([1-9]|1[0-2]))) (\*|([0-6])|(\*\/([0-6])))$/'),

                        Forms\Components\Select::make('frequency_parameters.timezone')
                            ->label('Timezone')
                            ->options(array_combine(
                                timezone_identifiers_list(),
                                timezone_identifiers_list()
                            ))
                            ->searchable()
                            ->placeholder('Default: UTC')
                    ]),

                Forms\Components\Section::make('Task Configuration')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Task Type')
                            ->options($registry->getTaskOptions())
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $type = $state;
                                $set('configuration', $type::getDefaultConfiguration());
                            }),
                            
                        Forms\Components\Grid::make()
                            ->schema(function (Forms\Get $get) use ($registry) {
                                $type = $get('type');
                                if (!$type) {
                                    return [];
                                }
                                if (class_exists($type) && method_exists($type, 'getConfigurationSchema')) {
                                    return collect($type::getConfigurationSchema())->toArray();
                                }
                                return [];
                            })
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('frequency_type')
                    ->description(fn (Task $record): string => static::formatScheduleDescription($record))
                    ->sortable()
                    ->formatStateUsing(fn (string $state) => str($state)->replace('_', ' ')->title()),
                // Tables\Columns\TextColumn::make('frequency')
                //     ->description(fn (Task $record): string => static::formatScheduleDescription($record))
                //     ->searchable()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('last_run_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_run_status')
                    ->badge()
                    ->default('unknown')
                    ->color(fn ($state): string => match ($state ?? 'unknown') {
                        'success' => 'success',
                        'running' => 'info',
                        'failed' => 'danger',
                        'unknown' => 'gray',
                        default => 'warning',
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(fn () => app(TaskRegistry::class)->getTaskOptions()),
                Tables\Filters\SelectFilter::make('frequency_type')
                    ->options([
                        'time_based' => 'Time-based Schedule',
                        'cron' => 'Cron Expression',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
                Tables\Filters\SelectFilter::make('last_run_status')
                    ->options([
                        'success' => 'Success',
                        'running' => 'Running',
                        'failed' => 'Failed',
                        'unknown' => 'Unknown',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (empty($data['value'])) {
                            return $query;
                        }
                        
                        if ($data['value'] === 'unknown') {
                            return $query->whereNull('last_run_status');
                        }
                        
                        return $query->where('last_run_status', $data['value']);
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('run')
                    ->icon('heroicon-m-play')
                    ->color('success')
                    ->action(function (Task $record) {
                        \App\Jobs\ExecuteScheduledTask::dispatch($record);
                        
                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Task queued')
                            ->body('The task has been queued for execution')
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->icon('heroicon-m-check')
                        ->color('success')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->icon('heroicon-m-x-mark')
                        ->color('danger')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false])),
                ]),
            ]);
    }

    protected static function getFrequencyOptions(): array 
    {
        return [
            'everySecond' => 'Every Second',
            'everyTwoSeconds' => 'Every Two Seconds',
            'everyFiveSeconds' => 'Every Five Seconds',
            'everyTenSeconds' => 'Every Ten Seconds',
            'everyFifteenSeconds' => 'Every Fifteen Seconds',
            'everyTwentySeconds' => 'Every Twenty Seconds',
            'everyThirtySeconds' => 'Every Thirty Seconds',
            'everyMinute' => 'Every Minute',
            'everyTwoMinutes' => 'Every Two Minutes',
            'everyThreeMinutes' => 'Every Three Minutes',
            'everyFourMinutes' => 'Every Four Minutes',
            'everyFiveMinutes' => 'Every Five Minutes',
            'everyTenMinutes' => 'Every Ten Minutes',
            'everyFifteenMinutes' => 'Every Fifteen Minutes',
            'everyThirtyMinutes' => 'Every Thirty Minutes',
            'hourly' => 'Every Hour',
            'hourlyAt' => 'Hourly At Specific Minute',
            'everyOddHour' => 'Every Odd Hour',
            'everyTwoHours' => 'Every Two Hours',
            'everyThreeHours' => 'Every Three Hours',
            'everyFourHours' => 'Every Four Hours',
            'everySixHours' => 'Every Six Hours',
            'daily' => 'Daily at Midnight',
            'dailyAt' => 'Daily at Specific Time',
            'twiceDaily' => 'Twice Daily',
            'twiceDailyAt' => 'Twice Daily at Specific Minutes',
            'weekly' => 'Weekly on Sunday at Midnight',
            'weeklyOn' => 'Weekly on Specific Day and Time',
            'monthly' => 'Monthly on First Day at Midnight',
            'monthlyOn' => 'Monthly on Specific Day and Time',
            'twiceMonthly' => 'Twice Monthly',
            'lastDayOfMonth' => 'Last Day of Month',
            'quarterly' => 'Quarterly on First Day',
            'quarterlyOn' => 'Quarterly on Specific Day',
            'yearly' => 'Yearly on First Day',
            'yearlyOn' => 'Yearly on Specific Date',
        ];
    }

    protected static function getFrequencyParameterFields(): array 
    {
        return [
            // For hourlyAt
            Forms\Components\Select::make('frequency_parameters.minute')
                ->options(array_combine(range(0, 59), range(0, 59)))
                ->visible(fn (Forms\Get $get) => in_array($get('frequency'), ['hourlyAt', 'everyOddHour', 'everyTwoHours', 'everyThreeHours', 'everyFourHours', 'everySixHours']))
                ->required(fn (Forms\Get $get) => in_array($get('frequency'), ['hourlyAt', 'everyOddHour', 'everyTwoHours', 'everyThreeHours', 'everyFourHours', 'everySixHours']))
                ->label('Minute'),

            // For dailyAt
            Forms\Components\TimePicker::make('frequency_parameters.time')
                ->visible(fn (Forms\Get $get) => in_array($get('frequency'), ['dailyAt', 'lastDayOfMonth']))
                ->required(fn (Forms\Get $get) => in_array($get('frequency'), ['dailyAt', 'lastDayOfMonth']))
                ->seconds(false)
                ->label('Time'),

            // For twiceDaily
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.first_hour')
                        ->options(array_combine(range(0, 23), range(0, 23)))
                        ->label('First Hour'),
                    Forms\Components\Select::make('frequency_parameters.second_hour')
                        ->options(array_combine(range(0, 23), range(0, 23)))
                        ->label('Second Hour'),
                ])
                ->visible(fn (Forms\Get $get) => $get('frequency') === 'twiceDaily')
                ->columns(2),

            // For twiceDailyAt
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.first_hour')
                        ->options(array_combine(range(0, 23), range(0, 23)))
                        ->label('First Hour'),
                    Forms\Components\Select::make('frequency_parameters.second_hour')
                        ->options(array_combine(range(0, 23), range(0, 23)))
                        ->label('Second Hour'),
                    Forms\Components\Select::make('frequency_parameters.minute')
                        ->options(array_combine(range(0, 59), range(0, 59)))
                        ->label('Minute'),
                ])
                ->visible(fn (Forms\Get $get) => $get('frequency') === 'twiceDailyAt')
                ->columns(3),

            // For weeklyOn
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.day')
                        ->options([
                            0 => 'Sunday',
                            1 => 'Monday',
                            2 => 'Tuesday',
                            3 => 'Wednesday',
                            4 => 'Thursday',
                            5 => 'Friday',
                            6 => 'Saturday',
                        ])
                        ->label('Day'),
                    Forms\Components\TimePicker::make('frequency_parameters.time')
                        ->seconds(false)
                        ->label('Time'),
                ])
                ->visible(fn (Forms\Get $get) => $get('frequency') === 'weeklyOn')
                ->columns(2),

            // For monthlyOn and quarterlyOn
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.day')
                        ->options(array_combine(range(1, 31), range(1, 31)))
                        ->label('Day of Month'),
                    Forms\Components\TimePicker::make('frequency_parameters.time')
                        ->seconds(false)
                        ->label('Time'),
                ])
                ->visible(fn (Forms\Get $get) => in_array($get('frequency'), ['monthlyOn', 'quarterlyOn']))
                ->columns(2),

            // For twiceMonthly
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.first_day')
                        ->options(array_combine(range(1, 31), range(1, 31)))
                        ->label('First Day'),
                    Forms\Components\Select::make('frequency_parameters.second_day')
                        ->options(array_combine(range(1, 31), range(1, 31)))
                        ->label('Second Day'),
                    Forms\Components\TimePicker::make('frequency_parameters.time')
                        ->seconds(false)
                        ->label('Time'),
                ])
                ->visible(fn (Forms\Get $get) => $get('frequency') === 'twiceMonthly')
                ->columns(3),

            // For yearlyOn
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('frequency_parameters.month')
                        ->options([
                            1 => 'January',
                            2 => 'February',
                            3 => 'March',
                            4 => 'April',
                            5 => 'May',
                            6 => 'June',
                            7 => 'July',
                            8 => 'August',
                            9 => 'September',
                            10 => 'October',
                            11 => 'November',
                            12 => 'December',
                        ])
                        ->label('Month'),
                    Forms\Components\Select::make('frequency_parameters.day')
                        ->options(array_combine(range(1, 31), range(1, 31)))
                        ->label('Day'),
                    Forms\Components\TimePicker::make('frequency_parameters.time')
                        ->seconds(false)
                        ->label('Time'),
                ])
                ->visible(fn (Forms\Get $get) => $get('frequency') === 'yearlyOn')
                ->columns(3),
        ];
    }

    // protected static function formatScheduleDescription(Task $task): string 
    // {
    //     if ($task->frequency_type === 'cron') {
    //         return $task->frequency_parameters['cron'] ?? '';
    //     }

    //     return match ($task->frequency) {
    //         'hourlyAt' => "At minute {$task->frequency_parameters['minute']}",
    //         'dailyAt' => "At {$task->frequency_parameters['time']}",
    //         'weeklyOn' => "On " . [
    //             'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
    //         ][$task->frequency_parameters['day']] . " at {$task->frequency_parameters['time']}",
    //         'monthlyOn' => "On day {$task->frequency_parameters['day']} at {$task->frequency_parameters['time']}",
    //         'twiceDaily' => "At {$task->frequency_parameters['first_hour']}:00 and {$task->frequency_parameters['second_hour']}:00",
    //         'twiceDailyAt' => "At {$task->frequency_parameters['first_hour']}:{$task->frequency_parameters['minute']} and {$task->frequency_parameters['second_hour']}:{$task->frequency_parameters['minute']}",
    //         default => str($task->frequency)->title(),
    //     };
    // }

    protected static function formatScheduleDescription(Task $task): string 
    {
        $params = $task->frequency_parameters ?: [];
        
        if ($task->frequency_type === 'cron') {
            return ($params['cron'] ?? '');
        }

        return match ($task->frequency) {
            // Simple frequencies
            'everySecond' => 'Every second',
            'everyTwoSeconds' => 'Every 2 seconds',
            'everyFiveSeconds' => 'Every 5 seconds',
            'everyTenSeconds' => 'Every 10 seconds',
            'everyFifteenSeconds' => 'Every 15 seconds',
            'everyTwentySeconds' => 'Every 20 seconds',
            'everyThirtySeconds' => 'Every 30 seconds',
            'everyMinute' => 'Every minute',
            'everyTwoMinutes' => 'Every 2 minutes',
            'everyThreeMinutes' => 'Every 3 minutes',
            'everyFourMinutes' => 'Every 4 minutes',
            'everyFiveMinutes' => 'Every 5 minutes',
            'everyTenMinutes' => 'Every 10 minutes',
            'everyFifteenMinutes' => 'Every 15 minutes',
            'everyThirtyMinutes' => 'Every 30 minutes',
            'hourly' => 'Every hour at :00',
            'everyOddHour' => "Every odd hour at minute " . ($params['minute'] ?? '0'),
            'everyTwoHours' => "Every 2 hours at minute " . ($params['minute'] ?? '0'),
            'everyThreeHours' => "Every 3 hours at minute " . ($params['minute'] ?? '0'),
            'everyFourHours' => "Every 4 hours at minute " . ($params['minute'] ?? '0'),
            'everySixHours' => "Every 6 hours at minute " . ($params['minute'] ?? '0'),
            'daily' => 'Every day at 00:00',
            'weekly' => 'Every Sunday at 00:00',
            'monthly' => 'First day of every month at 00:00',
            'quarterly' => 'First day of every quarter at 00:00',
            'yearly' => 'January 1 at 00:00',
            
            // Frequencies with parameters
            'hourlyAt' => "Every hour at minute " . ($params['minute'] ?? '0'),
            'dailyAt' => "Every day at " . ($params['time'] ?? '00:00'),
            'twiceDaily' => "Daily at " . ($params['first_hour'] ?? '0') . ":00 and " . ($params['second_hour'] ?? '0') . ":00",
            'twiceDailyAt' => "Daily at " . ($params['first_hour'] ?? '0') . ":" . ($params['minute'] ?? '0') . " and " . ($params['second_hour'] ?? '0') . ":" . ($params['minute'] ?? '0'),
            'weeklyOn' => "Every " . ([
                0 => 'Sunday', 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 
                4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday'
            ][$params['day'] ?? 0]) . " at " . ($params['time'] ?? '00:00'),
            'monthlyOn' => "Monthly on day " . ($params['day'] ?? '1') . " at " . ($params['time'] ?? '00:00'),
            'twiceMonthly' => "Monthly on days " . ($params['first_day'] ?? '1') . " and " . ($params['second_day'] ?? '15') . " at " . ($params['time'] ?? '00:00'),
            'lastDayOfMonth' => "Last day of each month at " . ($params['time'] ?? '00:00'),
            'quarterlyOn' => "Start of each quarter on day " . ($params['day'] ?? '1') . " at " . ($params['time'] ?? '00:00'),
            'yearlyOn' => "Annually on " . ([
                1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April', 5 => 'May', 6 => 'June',
                7 => 'July', 8 => 'August', 9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
            ][$params['month'] ?? 1]) . " " . ($params['day'] ?? '1') . " at " . ($params['time'] ?? '00:00'),
            
            default => str($task->frequency)->headline(),
        };
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ExecutionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTasks::route('/'),
            'create' => Pages\CreateTask::route('/create'),
            'edit' => Pages\EditTask::route('/{record}/edit'),
        ];
    }
}

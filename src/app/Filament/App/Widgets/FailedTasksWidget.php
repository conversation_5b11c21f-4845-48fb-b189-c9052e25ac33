<?php

namespace App\Filament\App\Widgets;

use App\Models\Task;
use App\Models\TaskExecution;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class FailedTasksWidget extends BaseWidget
{
    protected static ?string $heading = 'Recently Failed Tasks';
    protected static ?string $pollingInterval = '20s';

    protected static ?int $sort = 5;

    public function table(Table $table): Table
    {
        // Get tasks that have recently failed
        $failedTaskIds = TaskExecution::where('status', 'failed')
            ->where('completed_at', '>=', now()->subDays(3))
            ->pluck('task_id')
            ->unique();

        return $table
            ->query(
                Task::whereIn('id', $failedTaskIds)
                    ->where('is_active', true)
            )
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_run_at')
                    ->dateTime(),
                TextColumn::make('lastExecution.output')
                    ->label('Error Message')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        return $column->getState();
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('retry')
                    ->icon('heroicon-m-arrow-path')
                    ->color('warning')
                    ->action(function (Task $record) {
                        \App\Jobs\ExecuteScheduledTask::dispatch($record);

                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Task queued')
                            ->body('The task has been queued for execution')
                            ->send();
                    }),
            ]);
    }
}

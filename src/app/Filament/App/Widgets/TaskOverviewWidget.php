<?php

namespace App\Filament\App\Widgets;

use App\Models\Task;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TaskOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '10s';

    protected function getStats(): array
    {
        $totalTasks = Task::count();
        $activeTasks = Task::where('is_active', true)->count();
        $inactiveTasks = $totalTasks - $activeTasks;
        $failedTasks = Task::where('last_run_status', 'failed')->count();

        return [
            Stat::make('Total Tasks', $totalTasks)
                ->description('All configured tasks')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('primary'),

            Stat::make('Active Tasks', $activeTasks)
                ->description('Currently scheduled')
                ->descriptionIcon('heroicon-m-bolt')
                ->color('success'),

            Stat::make('Inactive Tasks', $inactiveTasks)
                ->description('Not currently scheduled')
                ->descriptionIcon('heroicon-m-bolt-slash')
                ->color('gray'),

            Stat::make('Failed Tasks', $failedTasks)
                ->description('Last execution failed')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color('danger'),
        ];
    }
}

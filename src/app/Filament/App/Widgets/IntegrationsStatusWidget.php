<?php

namespace App\Filament\App\Widgets;

use App\Models\Integration;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Services\IntegrationHealthChecker;

class IntegrationsStatusWidget extends BaseWidget
{
    protected static ?string $heading = 'Failing Integrations';
    protected static ?string $pollingInterval = '5m';

    protected static ?int $sort = 6;

    public function table(Table $table): Table
    {
        $healthChecker = app(IntegrationHealthChecker::class);

        // Only show failing integrations (status === false or is_string)
        $allIntegrations = Integration::all();
        $failingIntegrationIds = [];
        foreach ($allIntegrations as $integration) {
            $status = $healthChecker->check($integration);
            if ($status === false || is_string($status)) {
                $failingIntegrationIds[] = $integration->id;
            }
        }

        return $table
            ->query(
                Integration::query()->whereIn('id', $failingIntegrationIds)
            )
            ->emptyStateHeading('No failing integrations')
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->formatStateUsing(function (string $state): string {
                        if (class_exists($state) && method_exists($state, 'getName')) {
                            return $state::getName();
                        }
                        return $state;
                    }),
                IconColumn::make('health_status')
                    ->label('Status')
                    ->getStateUsing(function (Integration $record) use ($healthChecker) {
                        $status = $healthChecker->check($record);
                        if ($status === true) {
                            return 'healthy';
                        } elseif ($status === false) {
                            return 'unhealthy';
                        } elseif ($status === null) {
                            return 'unknown';
                        }
                        return 'unknown';
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'healthy' => 'heroicon-o-check-circle',
                        'unhealthy' => 'heroicon-o-exclamation-circle',
                        'unknown' => 'heroicon-o-minus-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'healthy' => 'success',
                        'unhealthy' => 'danger',
                        'unknown' => 'secondary',
                        default => 'secondary',
                    }),
                TextColumn::make('last_checked_at')
                    ->getStateUsing(function (Integration $record) use ($healthChecker) {
                        $lastCheck = $healthChecker->lastChecked($record);
                        return $lastCheck ? $lastCheck->diffForHumans() : 'Never';
                    }),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('check')
                    ->icon('heroicon-m-arrow-path')
                    ->action(function (Integration $record, IntegrationHealthChecker $healthChecker) {
                        $isHealthy = $healthChecker->check($record, true);

                        \Filament\Notifications\Notification::make()
                            ->title('Integration Health Check')
                            ->body($isHealthy ? 'Integration is healthy' : 'Integration check failed')
                            ->color($isHealthy ? 'success' : 'danger')
                            ->send();
                    }),
            ]);
    }
}

<?php

namespace App\Filament\App\Widgets;

use App\Models\TaskExecution;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class RecentTaskExecutionsWidget extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $pollingInterval = '15s';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                TaskExecution::query()
                    ->with('task')
                    ->latest('started_at')
                    ->limit(5)
            )
            ->columns([
                TextColumn::make('task.name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'success' => 'success',
                        'running' => 'info',
                        'failed' => 'danger',
                        default => 'warning',
                    }),
                TextColumn::make('started_at')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('runtime_seconds')
                    ->label('Runtime')
                    ->formatStateUsing(fn ($state) => $state . ' seconds'),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('view')
                    ->url(fn (TaskExecution $record) => route('filament.app.resources.tasks.edit', ['record' => $record->task_id]) . '?activeRelationManager=1')
                    ->icon('heroicon-m-eye'),
            ]);
    }
}

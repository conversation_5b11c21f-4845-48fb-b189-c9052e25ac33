<?php
namespace App\Services;

use Carbon\Carbon;

class TaskScheduler
{
    public function applySchedule($event, string $frequency, array $parameters): void
    {
        match($frequency) {
            // Second-based schedules
            'everySecond' => $event->everySecond(),
            'everyTwoSeconds' => $event->everyTwoSeconds(),
            'everyFiveSeconds' => $event->everyFiveSeconds(),
            'everyTenSeconds' => $event->everyTenSeconds(),
            'everyFifteenSeconds' => $event->everyFifteenSeconds(),
            'everyTwentySeconds' => $event->everyTwentySeconds(),
            'everyThirtySeconds' => $event->everyThirtySeconds(),
            
            // Minute-based schedules
            'everyMinute' => $event->everyMinute(),
            'everyTwoMinutes' => $event->everyTwoMinutes(),
            'everyThreeMinutes' => $event->everyThreeMinutes(),
            'everyFourMinutes' => $event->everyFourMinutes(),
            'everyFiveMinutes' => $event->everyFiveMinutes(),
            'everyTenMinutes' => $event->everyTenMinutes(),
            'everyFifteenMinutes' => $event->everyFifteenMinutes(),
            'everyThirtyMinutes' => $event->everyThirtyMinutes(),
            
            // Hour-based schedules
            'hourly' => $event->hourly(),
            'hourlyAt' => $event->hourlyAt($parameters['minute']),
            'everyOddHour' => $event->everyOddHour($parameters['minute'] ?? 0),
            'everyTwoHours' => $event->everyTwoHours($parameters['minute'] ?? 0),
            'everyThreeHours' => $event->everyThreeHours($parameters['minute'] ?? 0),
            'everyFourHours' => $event->everyFourHours($parameters['minute'] ?? 0),
            'everySixHours' => $event->everySixHours($parameters['minute'] ?? 0),
            
            // Daily schedules
            'daily' => $event->daily(),
            'dailyAt' => $event->dailyAt($parameters['time']),
            'twiceDaily' => $event->twiceDaily(
                $parameters['first_hour'], 
                $parameters['second_hour']
            ),
            'twiceDailyAt' => $event->twiceDailyAt(
                $parameters['first_hour'],
                $parameters['second_hour'],
                $parameters['minute']
            ),
            
            // Weekly schedules
            'weekly' => $event->weekly(),
            'weeklyOn' => $event->weeklyOn(
                $parameters['day'], 
                $parameters['time']
            ),
            
            // Monthly schedules
            'monthly' => $event->monthly(),
            'monthlyOn' => $event->monthlyOn(
                $parameters['day'],
                $parameters['time']
            ),
            'twiceMonthly' => $event->twiceMonthly(
                $parameters['first_day'],
                $parameters['second_day'],
                $parameters['time']
            ),
            'lastDayOfMonth' => $event->lastDayOfMonth($parameters['time']),
            
            // Quarterly schedules
            'quarterly' => $event->quarterly(),
            'quarterlyOn' => $event->quarterlyOn(
                $parameters['day'],
                $parameters['time']
            ),
            
            // Yearly schedules
            'yearly' => $event->yearly(),
            'yearlyOn' => $event->yearlyOn(
                $parameters['month'],
                $parameters['day'],
                $parameters['time']
            ),
            
            default => throw new \InvalidArgumentException("Unknown frequency: {$frequency}")
        };
    }
}

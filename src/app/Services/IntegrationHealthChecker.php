<?php

namespace App\Services;

use App\Models\Integration;
use App\Scheduling\Integrations\S3BucketIntegration;
use App\Scheduling\Integrations\PostgreSQLServerIntegration;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class IntegrationHealthChecker
{
    /**
     * Check the health of an integration
     *
     * @param Integration $integration
     * @param bool $forceCheck Force a new check instead of using cached results
     * @return bool|string|null true=healthy, false=unhealthy, string=unhealthy with message, null=not implemented
     */
    public function check(Integration $integration, bool $forceCheck = false)
    {
        $cacheKey = "integration_health_{$integration->id}";

        if (!$forceCheck && Cache::has($cacheKey)) {
            return Cache::get($cacheKey)['status'];
        }

        $status = null;
        $integrationClass = $integration->type;

        try {
            // Load the integration configuration with resolved credentials
            $config = $this->resolveIntegrationConfig($integration);

            if (!$config) {
                // If config can't be resolved, treat as unknown (not unhealthy)
                $status = null;
            } elseif (class_exists($integrationClass) && method_exists($integrationClass, 'getHealthStatus')) {
                $status = $integrationClass::getHealthStatus($config);
                // If the method returns null, treat as unknown (not unhealthy)
                if ($status === null) {
                    $status = null;
                } elseif ($status === true) {
                    $status = true;
                } elseif ($status === false) {
                    $status = false;
                } elseif (is_string($status)) {
                    // unhealthy with message
                    $status = $status;
                } else {
                    // fallback for unexpected return types
                    $status = null;
                }
            } else {
                // If the method does not exist, treat as unknown (not unhealthy)
                $status = null;
            }
        } catch (\Throwable $e) {
            // Only set to false if the method exists and threw an error AND the method is implemented
            if (class_exists($integrationClass) && method_exists($integrationClass, 'getHealthStatus')) {
                $status = false;
            } else {
                $status = null;
            }
        }

        // Cache the result
        Cache::put($cacheKey, [
            'status' => $status,
            'checked_at' => now(),
        ], now()->addHours(1));

        return $status;
    }

    /**
     * Get the last time the integration was checked
     *
     * @param Integration $integration
     * @return \Carbon\Carbon|null
     */
    public function lastChecked(Integration $integration): ?Carbon
    {
        $cacheKey = "integration_health_{$integration->id}";

        if (Cache::has($cacheKey)) {
            return Carbon::parse(Cache::get($cacheKey)['checked_at']);
        }

        return null;
    }

    /**
     * Resolve the integration configuration with credentials
     * 
     * @param Integration $integration
     * @return array|null
     */
    private function resolveIntegrationConfig(Integration $integration): ?array
    {
        $integrationClass = $integration->type;

        if (!class_exists($integrationClass) || !method_exists($integrationClass, 'loadIntegration')) {
            return null;
        }

        return $integrationClass::loadIntegration($integration->id);
    }
}

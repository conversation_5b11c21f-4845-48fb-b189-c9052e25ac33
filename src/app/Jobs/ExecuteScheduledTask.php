<?php

namespace App\Jobs;

use App\Models\Task;
use App\Models\TaskExecution;
use App\Scheduling\TaskRegistry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ExecuteScheduledTask implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected Task $task
    ) {}

    public function handle(TaskRegistry $registry): void
    {
        $execution = new TaskExecution([
            'task_id' => $this->task->id,
            'started_at' => now(),
            'status' => 'running',
        ]);
        $execution->save();

        $this->task->forceFill([
            'last_run_status' => 'running',
            'last_run_at' => now(),
        ])->save();

        $startTime = microtime(true);
        $output = null;
        $error = null;

        try {
            $taskInstance = $registry->resolveTask(
                $this->task->type,
                $this->task->id,
                $this->task->configuration
            );

            ob_start();
            try {
                $taskInstance->handle();
                $output = ob_get_clean();
            } catch (\Throwable $e) {
                $output = ob_get_clean();
                throw $e;
            }

            $endTime = microtime(true);
            $runtimeSeconds = $endTime - $startTime;

            $execution->forceFill([
                'completed_at' => now(),
                'status' => 'success',
                'output' => $output,
                'runtime_seconds' => $runtimeSeconds,
            ])->save();

            $this->task->forceFill([
                'last_run_status' => 'success',
            ])->save();
        } catch (\Throwable $e) {
            Log::error('Task execution failed', [
                'task_id' => $this->task->id,
                'execution_id' => $execution->id,
                'error' => $e->getMessage(),
            ]);

            $endTime = microtime(true);
            $runtimeSeconds = $endTime - $startTime;

            $error = $e->getMessage() . "\n" . $e->getTraceAsString();

            $execution->forceFill([
                'completed_at' => now(),
                'status' => 'failed',
                'output' => $output,
                'error' => $error,
                'runtime_seconds' => $runtimeSeconds,
            ])->save();

            $this->task->forceFill([
                'last_run_status' => 'failed',
            ])->save();

            throw $e;
        }
    }
}

<?php

namespace App\Scheduling;

use App\Contracts\Integratable;
use Filament\Forms\Components\Select;
use App\Models\Credential;
use Illuminate\Support\Str;

abstract class AbstractIntegration implements Integratable
{
    protected const FIELD_PREFIX = 'configuration.';

    /**
     * Returns the health status of the integration.
     * By default, returns null (not implemented).
     * Override in concrete integrations to provide health check logic.
     *
     * @param array|null $config
     * @return bool|string|null true=healthy, false=unhealthy, string=unhealthy with message, null=not implemented
     */
    public static function getHealthStatus(?array $config = null)
    {
        return null;
    }

    public static function getDescription(): string
    {
        return '';
    }

    public static function getDefaultConfiguration(): array
    {
        return [];
    }

    public static function getConfigurationSchema(): array
    {
        return [];
    }

    protected static function field(string $field): string
    {
        return static::FIELD_PREFIX . $field;
    }

    protected static function credentialSelect(string $name, string $credentialTypeClass, bool $isMultiple = false): Select
    {
        return Select::make(static::field($name))
            ->label(Str::of($name)->snake()->replace('_', ' ')->title()->toString())
            ->options(function () use ($credentialTypeClass) {
                return Credential::where('type', $credentialTypeClass)
                    ->pluck('name', 'id')
                    ->all();
            })
            ->searchable()
            ->when($isMultiple, fn (Select $select) => $select->multiple());
    }
}

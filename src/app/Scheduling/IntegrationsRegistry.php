<?php

namespace App\Scheduling;

use App\Contracts\Integratable;
use Illuminate\Support\Collection;

class IntegrationsRegistry
{
    private array $integrations = [];

    public function register(string $integrationClass): void
    {
        if (!is_subclass_of($integrationClass, Integratable::class)) {
            throw new \InvalidArgumentException(
                "Integration class must implement " . Integratable::class
            );
        }

        $this->integrations[$integrationClass] = $integrationClass;
    }

    public function getIntegrationOptions(): array
    {
        return Collection::make($this->integrations)
            ->mapWithKeys(fn ($class) => [
                $class => $class::getName()
            ])
            ->toArray();
    }

    public function resolveIntegration(string $class): Integratable
    {
        if (!isset($this->integrations[$class])) {
            throw new \InvalidArgumentException("Unknown integration type: {$class}");
        }

        return new $class();
    }
}

<?php

namespace App\Scheduling;

use App\Contracts\Credentiable;
use Illuminate\Support\Collection;

class CredentialsRegistry
{
    private array $credentials = [];

    public function register(string $credentialClass): void
    {
        if (!is_subclass_of($credentialClass, Credentiable::class)) {
            throw new \InvalidArgumentException(
                "Credential class must implement " . Credentiable::class
            );
        }

        $this->credentials[$credentialClass] = $credentialClass;
    }

    public function getCredentialOptions(): array
    {
        return Collection::make($this->credentials)
            ->mapWithKeys(fn ($class) => [
                $class => $class::getName()
            ])
            ->toArray();
    }

    public function resolveCredential(string $class): Credentiable
    {
        if (!isset($this->credentials[$class])) {
            throw new \InvalidArgumentException("Unknown credential type: {$class}");
        }

        return new $class();
    }
}

<?php

namespace App\Scheduling;

use App\Contracts\Taskable;
use Filament\Forms\Components\Select;
use App\Models\Credential;
use App\Models\Integration;
use Illuminate\Support\Str;

abstract class AbstractTask implements Taskable
{
    protected const FIELD_PREFIX = 'configuration.';

    public function __construct(
        protected string $taskId,
        protected array $configuration
    ) {}

    public static function getDescription(): string
    {
        return '';
    }

    public static function getDefaultConfiguration(): array
    {
        return [];
    }

    public static function getConfigurationSchema(): array
    {
        return [];
    }
    
    protected static function field(string $field): string
    {
        return static::FIELD_PREFIX . $field;
    }

    protected static function credentialSelect(string $name, string $credentialTypeClass, bool $isMultiple = false): Select
    {
        return Select::make(static::field($name))
            ->label(Str::of($name)->snake()->replace('_', ' ')->title()->toString())
            ->options(function () use ($credentialTypeClass) {
                return Credential::where('type', $credentialTypeClass)
                    ->pluck('name', 'id')
                    ->all();
            })
            ->searchable()
            ->when($isMultiple, fn (Select $select) => $select->multiple());
    }

    protected static function integrationSelect(string $name, string $integrationTypeClass, bool $isMultiple = false): Select
    {
        return Select::make(static::field($name))
            ->label(Str::of($name)->snake()->replace('_', ' ')->title()->toString())
            ->options(function () use ($integrationTypeClass) {
                return Integration::where('type', $integrationTypeClass)
                    ->pluck('name', 'id')
                    ->all();
            })
            ->searchable()
            ->when($isMultiple, fn (Select $select) => $select->multiple());
    }

    public static function loadCredentials(string $credentialId): ?array
    {
        $credential = Credential::find($credentialId);

        if (!$credential) {
            return null;
        }
        return $credential->configuration;
    }

    public static function loadIntegration(string $integrationId): ?array
    {
        $integration = Integration::find($integrationId);

        if (!$integration) {
            return null;
        }

        foreach ($integration->configuration as $key => $value) {
            if (Str::endsWith($key, 'credential_id')) {
                $credentials = static::loadCredentials($value);

                if ($credentials) {
                    $resolvedPropertyName = Str::replace('credential_id', 'credentials', $key);
                    $configuration = $integration->configuration;
                    $configuration[$resolvedPropertyName] = $credentials;
                    return $configuration;
                }
            }
        }
        return $integration->configuration;
    }
}

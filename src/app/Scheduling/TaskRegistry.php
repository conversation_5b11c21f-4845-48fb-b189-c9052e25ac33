<?php

namespace App\Scheduling;

use App\Contracts\Taskable;
use Illuminate\Support\Collection;

class TaskRegistry
{
    private array $tasks = [];

    public function register(string $taskClass): void
    {
        if (!is_subclass_of($taskClass, Taskable::class)) {
            throw new \InvalidArgumentException(
                "Task class must implement " . Taskable::class
            );
        }

        $this->tasks[$taskClass] = $taskClass;
    }

    public function getTaskOptions(): array
    {
        return Collection::make($this->tasks)
            ->mapWithKeys(fn ($class) => [
                $class => $class::getName()
            ])
            ->toArray();
    }

    public function resolveTask(string $class, string $taskId, array $configuration): Taskable
    {
        if (!isset($this->tasks[$class])) {
            throw new \InvalidArgumentException("Unknown task type: {$class}");
        }

        return new $class($taskId, $configuration);
    }
}

<?php

namespace App\Scheduling;

use App\Contracts\Credentiable;

abstract class AbstractCredential implements Credentiable
{
    protected const FIELD_PREFIX = 'configuration.';

    public static function getDescription(): string
    {
        return '';
    }

    public static function getDefaultConfiguration(): array
    {
        return [];
    }

    public static function getConfigurationSchema(): array
    {
        return [];
    }

    protected static function field(string $field): string
    {
        return static::FIELD_PREFIX . $field;
    }
}

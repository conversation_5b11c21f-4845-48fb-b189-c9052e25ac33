<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Encrypted;

class Credential extends Model
{
    use HasUlids;

    protected $fillable = [
        'name',
        'description',
        'type',
        'configuration',
    ];

    protected $casts = [
        'configuration' => 'encrypted:array',
    ];
}

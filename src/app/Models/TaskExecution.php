<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaskExecution extends Model
{
    /** @use HasUlids */
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'task_id',
        'started_at',
        'completed_at',
        'status',
        'output',
        'error',
        'runtime_seconds',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'runtime_seconds' => 'float',
        ];
    }

    /**
     * Get the task that owns the execution.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }
}
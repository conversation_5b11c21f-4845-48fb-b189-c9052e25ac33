<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    /** @use HasUlids */
    use HasUlids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'description',
        'is_active',
        'frequency_type',
        'frequency',
        'frequency_parameters',
        'type',
        'configuration',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        //
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'frequency_parameters' => 'array',
            'configuration' => 'encrypted:array',
        ];
    }

    /**
     * Get the executions for the task.
     */
    public function executions(): HasMany
    {
        return $this->hasMany(TaskExecution::class);
    }

    /**
     * Get the latest execution for the task.
     */
    public function latestExecution()
    {
        return $this->hasOne(TaskExecution::class)->latestOfMany();
    }
}

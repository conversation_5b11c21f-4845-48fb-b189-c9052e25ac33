{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "filament/filament": "^3.2", "laravel/framework": "^11.31", "laravel/tinker": "^2.9", "timokoerber/laravel-one-time-operations": "^1.4", "itbm/scheduler-credential-api-token": "~1.0", "itbm/scheduler-credential-aws": "~1.0", "itbm/scheduler-credential-password": "~1.0", "itbm/scheduler-credential-username-password": "~1.0", "itbm/scheduler-integration-postgresql-server": "~1.0", "itbm/scheduler-integration-s3-bucket": "~1.0", "itbm/scheduler-task-file-cleanup": "~1.0", "itbm/scheduler-task-postgresql-backup": "~1.0", "itbm/scheduler-task-shell-command": "~1.0", "itbm/scheduler-task-web-request": "~1.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "repositories": [{"type": "path", "url": "../packages/*", "options": {"symlink": false}}], "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}